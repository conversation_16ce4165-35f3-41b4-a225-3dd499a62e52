package me.hsgamer.extrastorage.api;

import me.hsgamer.extrastorage.api.user.User;
import org.bukkit.OfflinePlayer;

/**
 * Stub class for ExtraStorage StorageAPI
 */
public class StorageAPI {

    private static StorageAPI instance = new StorageAPI();

    public static StorageAPI getInstance() {
        return instance;
    }

    public static User getUser(OfflinePlayer player) {
        return new User(player);
    }
}
