# SbmagicTop - Discord Leaderboard Bot

Plugin Discord bot để hiển thị leaderboard từ AjLeaderboards lên Discord thông qua slash commands.

## Tính năng

- ✅ Slash command `/top` để hiển thị bảng xếp hạng
- ✅ Hỗ trợ parse placeholder từ AjLeaderboards
- ✅ Hệ thống config linh hoạt cho từng loại top
- ✅ Cache placeholder để tối ưu hiệu suất
- ✅ Chuyển đổi Minecraft color codes sang Discord
- ✅ Command reload config trong game
- ✅ Hỗ trợ nhiều loại leaderboard

## Cài đặt

1. **Yêu cầu:**
   - Paper/Spigot 1.21+
   - PlaceholderAPI
   - AjLeaderboards (tùy chọn)

2. **Cài đặt plugin:**
   - Tải file `.jar` và đặt vào thư mục `plugins/`
   - Khởi động server để tạo config

3. **Cấu hình Discord Bot:**
   - Tạo Discord Application tại [Discord Developer Portal](https://discord.com/developers/applications)
   - Tạo bot và lấy token
   - Mời bot vào server Discord với quyền:
     - Send Messages
     - Use Slash Commands
     - Embed Links

4. **Cấu hình plugin:**
   ```yaml
   # config.yml
   discord:
     token: "YOUR_BOT_TOKEN_HERE"
     guild-id: "YOUR_GUILD_ID_HERE"
   ```

## Sử dụng

### Slash Commands

- `/top balance` - Hiển thị top balance
- `/top playtime` - Hiển thị top playtime
- (Các loại top khác tùy theo config)

### Commands trong game

- `/sbmagictop reload` - Reload config (quyền: `sbmagictop.reload`)

## Cấu hình Top

Tạo file `.yml` trong thư mục `plugins/SbmagicTop/top/` để thêm loại top mới:

```yaml
# top/balance.yml
name: "💰 Top Balance"
description: "Bảng xếp hạng người giàu nhất server"
color: 0xFFD700
thumbnail: ""

content:
  - "&7⊂&e#1&7⊃ &a%ajlb_lb_vault_eco_balance_1_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_1_alltime_value% &r%img_coins%"
  - "&7⊂&e#2&7⊃ &a%ajlb_lb_vault_eco_balance_2_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_2_alltime_value% &r%img_coins%"
  # ... thêm các dòng khác
```

### Placeholder hỗ trợ

- **AjLeaderboards:** `%ajlb_lb_<type>_<rank>_<period>_<data>%`
- **Custom Images:** `%img_coins%`, `%img_diamond%`, `%img_star%`, `%img_trophy%`
- **Tất cả PlaceholderAPI placeholders**

### Màu sắc Embed

Sử dụng hex color code (ví dụ: `0xFFD700` cho màu vàng)

## Troubleshooting

### Bot không kết nối
- Kiểm tra token Discord bot
- Kiểm tra Guild ID
- Kiểm tra quyền bot trong Discord server

### Placeholder không hoạt động
- Kiểm tra AjLeaderboards đã cài đặt
- Kiểm tra PlaceholderAPI đã cài đặt
- Sử dụng `/sbmagictop reload` để reload config

### Lỗi khi hiển thị
- Kiểm tra format config file YAML
- Kiểm tra độ dài nội dung (Discord có giới hạn)

## API cho Developer

```java
// Lấy instance plugin
SbmagicTopPlugin plugin = SbmagicTopPlugin.getInstance();

// Parse placeholder
String result = plugin.getPlaceholderManager().parsePlaceholders("%ajlb_lb_vault_eco_balance_1_alltime_name%");

// Lấy config top
TopConfig config = plugin.getConfigManager().getTopConfig("balance");
```

## Changelog

### v1.0.0
- Phiên bản đầu tiên
- Hỗ trợ slash command `/top`
- Hệ thống config linh hoạt
- Parse placeholder AjLeaderboards

## Hỗ trợ

- Discord: [SoulMC Discord](https://discord.gg/soulmc)
- GitHub Issues: [Tạo issue mới](https://github.com/your-repo/issues)

## License

MIT License - Xem file LICENSE để biết thêm chi tiết.
