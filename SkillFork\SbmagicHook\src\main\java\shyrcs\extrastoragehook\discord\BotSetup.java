package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.discord.commands.CommandConnect;
import shyrcs.extrastoragehook.discord.commands.CommandSell;
import shyrcs.extrastoragehook.discord.commands.CommandStorage;
import shyrcs.extrastoragehook.discord.commands.CommandHelp;
import shyrcs.extrastoragehook.SbMagicHook;

import java.util.ArrayList;
import java.util.List;

/**
 * Setup class cho Discord bot
 */
public class BotSetup {
    
    private static boolean limited = false;
    private static List<String> whitelistedChannels = new ArrayList<>();
    
    /**
     * Đăng ký internal commands
     */
    public static void buildInternalCommands() {
        // Đăng ký các commands
        Library.manager.registerCommand("connect", new CommandConnect());
        Library.manager.registerCommand("sell", new CommandSell());
        Library.manager.registerCommand("storage", new CommandStorage());
        Library.manager.registerCommand("help", new CommandHelp());
        
        // Thiết lập channel restrictions
        whitelistedChannels = Library.config.getWhitelistedChannels();
        limited = !whitelistedChannels.isEmpty();
        
        SbMagicHook.info("Đã đăng ký " + Library.manager.getCommandCount() + " Discord commands");
    }
    
    /**
     * Đăng ký slash commands với Discord
     */
    public static void registerSlashCommands(JDA jda) {
        if (!Library.config.useSlashCommands()) {
            SbMagicHook.info("Slash commands đã bị tắt trong config");
            return;
        }
        
        try {
            jda.updateCommands().addCommands(
                Commands.slash("connect", "Tạo mã kết nối để liên kết tài khoản Discord với Minecraft"),
                Commands.slash("sell", "Bán items từ kho ExtraStorage")
                    .addOption(net.dv8tion.jda.api.interactions.commands.OptionType.STRING, "item", "Tên item cần bán", true)
                    .addOption(net.dv8tion.jda.api.interactions.commands.OptionType.INTEGER, "amount", "Số lượng cần bán", false),
                Commands.slash("storage", "Xem thông tin kho ExtraStorage"),
                Commands.slash("help", "Hiển thị danh sách lệnh")
            ).queue(
                success -> SbMagicHook.info("Đã đăng ký slash commands thành công!"),
                error -> SbMagicHook.error("Lỗi khi đăng ký slash commands: " + error.getMessage())
            );
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi đăng ký slash commands: " + e.getMessage());
        }
    }
    
    /**
     * Kiểm tra có giới hạn channel không
     */
    public static boolean isLimited() {
        return limited;
    }
    
    /**
     * Lấy danh sách whitelisted channels
     */
    public static List<String> getWhitelistedChannels() {
        return whitelistedChannels;
    }
    
    /**
     * Kiểm tra channel có được phép không
     */
    public static boolean isChannelAllowed(String channelId) {
        return !limited || whitelistedChannels.contains(channelId);
    }
}
