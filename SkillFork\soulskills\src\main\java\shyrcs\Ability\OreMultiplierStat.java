package shyrcs.Ability;

import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.mmoitem.ReadMMOItem;
import net.Indyuce.mmoitems.gui.edition.EditionInventory;
import net.Indyuce.mmoitems.stat.data.DoubleData;
import net.Indyuce.mmoitems.stat.type.DoubleStat;
import io.lumine.mythic.lib.api.item.ItemTag;
import io.lumine.mythic.lib.api.item.SupportedNBTTagValues;
import net.Indyuce.mmoitems.api.util.NumericStatFormula;
import org.bukkit.Material;
import org.bukkit.event.inventory.InventoryClickEvent;
import java.util.*;

public class OreMultiplierStat extends DoubleStat {
    
    public OreMultiplierStat() {
        super("ORE_MULTIPLIER",
              Material.DIAMOND_PICKAXE,
              "Ore Multiplier",
              new String[]{"Tỷ lệ kích hoạt Multiplier Ore.", "Req: <PERSON><PERSON><PERSON> hợp Ore Multiplier Amount.", "", "Đơn vị: % (phần trăm)"},
              new String[]{"tool", "all"}
        );
    }
    
    @Override
    public void whenApplied(ItemStackBuilder item, DoubleData data) {
        // Lưu giá trị vào NBT
        item.addItemTag(new ItemTag("MMOITEMS_ORE_MULTIPLIER", data.getValue()));

        // Thêm lore với giá trị cố định
        item.getLore().insert("ore-multiplier-rate", "§7Ore Multiplier Rate: §a" + String.format("%.1f", data.getValue()) + "%");
    }
    
    @Override
    public ArrayList<ItemTag> getAppliedNBT(DoubleData data) {
        ArrayList<ItemTag> tags = new ArrayList<>();
        tags.add(new ItemTag("MMOITEMS_ORE_MULTIPLIER", data.getValue()));
        return tags;
    }
    
    @Override
    public void whenDisplayed(List<String> lore, Optional<NumericStatFormula> statData) {
        if (statData.isPresent()) {
            lore.add("§7Ore Multiplier Rate: §a" + statData.get().toString() + "%");
        } else {
            lore.add("§7Ore Multiplier Rate: §c0%");
        }
    }
    
    @Override
    public DoubleData getLoadedNBT(ArrayList<ItemTag> tags) {
        ItemTag tag = ItemTag.getTagAtPath("MMOITEMS_ORE_MULTIPLIER", tags);
        return tag != null ? new DoubleData((Double) tag.getValue()) : null;
    }
    
    @Override
    public void whenLoaded(ReadMMOItem mmoItem) {
        // Get tags - Fixed approach following the Abilities class pattern
        ArrayList<ItemTag> relevantTags = new ArrayList<>();

        if (mmoItem.getNBT().hasTag("MMOITEMS_ORE_MULTIPLIER"))
            relevantTags.add(ItemTag.getTagAtPath("MMOITEMS_ORE_MULTIPLIER", mmoItem.getNBT(), SupportedNBTTagValues.DOUBLE));

        DoubleData data = getLoadedNBT(relevantTags);

        // Valid?
        if (data != null) {
            // Set
            mmoItem.setData(this, data);
        }
    }
    
    @Override
    public void whenInput(EditionInventory inv, String message, Object... info) {
        try {
            double value = Double.parseDouble(message);
            if (value < 0) {
                inv.getPlayer().sendMessage("§cGiá trị phải lớn hơn hoặc bằng 0!");
                return;
            }
            if (value > 100) {
                inv.getPlayer().sendMessage("§cGiá trị không được vượt quá 100%!");
                return;
            }
            
            inv.getEditedSection().set(getPath(), value);
            inv.registerTemplateEdition();
            inv.getPlayer().sendMessage("§aTỷ Lệ Ore Multiplier đã được đặt thành " + value + "%");
        } catch (NumberFormatException e) {
            inv.getPlayer().sendMessage("§cVui lòng nhập một số hợp lệ!");
        }
    }
    
    @Override
    public void whenClicked(EditionInventory inv, InventoryClickEvent event) {
        // Sử dụng hệ thống StatEdition của MMOItems
        new net.Indyuce.mmoitems.api.edition.StatEdition(inv, this).enable("&eNhập giá trị Ore Multiplier (0-100%):");
    }
    
    @Override
    public DoubleData getClearStatData() {
        return new DoubleData(0);
    }
}
