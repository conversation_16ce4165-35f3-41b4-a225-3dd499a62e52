<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<groupId>me.hsgamer</groupId>
	<artifactId>ExtraStorage</artifactId>
	<version>1.0</version>
	<packaging>jar</packaging>

	<name>ExtraStorage-Custom</name>
	
	<properties>
		<core.version>4.6.0</core.version>
		<java.version>21</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	
	<build>
		<finalName>${project.name}</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>3.5.1</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals><goal>shade</goal></goals>
						<configuration>
							<createDependencyReducedPom>false</createDependencyReducedPom>
							<artifactSet>
								<includes>
									<include>org.bstats:*</include>
									<include>me.hsgamer:hscore-*</include>
									<include>io.github.projectunified:*</include>
								</includes>
							</artifactSet>
							<relocations>
								<relocation>
									<pattern>org.bstats</pattern>
									<shadedPattern>me.hsgamer.extrastorage.lib.bstats</shadedPattern>
								</relocation>
								<relocation>
									<pattern>io.github.projectunified</pattern>
									<shadedPattern>me.hsgamer.extrastorage.lib.uniitem</shadedPattern>
								</relocation>
								<relocation>
									<pattern>me.hsgamer.hscore</pattern>
									<shadedPattern>me.hsgamer.extrastorage.lib.core</shadedPattern>
								</relocation>
							</relocations>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy file="${project.build.directory}/${project.build.finalName}.jar"
									  todir="C:\Users\<USER>\Desktop\Test Plugins\plugins" 
									  overwrite="true"/>
							</target>
						</configuration>
					</execution>
				</executions>
			</plugin> -->
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
	</build>
	
	<repositories>
		<repository>
			<id>spigotmc-repo</id>
			<url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
		</repository>
		<repository>
            <id>papermc</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
		<repository>
			<id>minecraft-repo</id>
			<url>https://libraries.minecraft.net/</url>
		</repository>
		<repository>
			<id>jitpack.io</id>
			<url>https://jitpack.io</url>
		</repository>
		<repository>
			<id>techscode</id>
			<url>https://repo.techscode.com/repository/techscode-apis/</url>
		</repository>
		<repository>
			<id>placeholderapi</id>
			<url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
		</repository>
		<repository>
			<id>bg-repo</id>
			<url>https://repo.bg-software.com/repository/api/</url>
		</repository>
		<repository>
			<id>rosewood-repo</id>
			<url>https://repo.rosewooddev.io/repository/public/</url>
		</repository>
		<repository>
			<id>songoda-plugins</id>
			<url>https://repo.songoda.com/repository/minecraft-plugins/</url>
		</repository>
		<repository>
			<id>nightexpress-releases</id>
			<url>https://repo.nightexpressdev.com/releases</url>
		</repository>
	</repositories>
	
	<dependencies>
		<dependency>
			<groupId>org.spigotmc</groupId>
			<artifactId>spigot-api</artifactId>
			<version>1.20.4-R0.1-SNAPSHOT</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
            <groupId>io.papermc.paper</groupId>
            <artifactId>paper-api</artifactId>
            <version>1.20.4-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.32</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.mojang</groupId>
			<artifactId>authlib</artifactId>
			<version>5.0.47</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.bstats</groupId>
			<artifactId>bstats-bukkit</artifactId>
			<version>3.0.2</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.arcaniax</groupId>
			<artifactId>HeadDatabase-API</artifactId>
			<version>1.3.1</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.MilkBowl</groupId>
			<artifactId>VaultAPI</artifactId>
			<version>1.7</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.brcdev-minecraft</groupId>
			<artifactId>shopgui-api</artifactId>
			<version>3.0.0</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.Gypopo</groupId>
			<artifactId>EconomyShopGUI-API</artifactId>
			<version>1.7.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.black_ixx</groupId>
			<artifactId>playerpoints</artifactId>
			<version>3.2.6</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.github.Realizedd</groupId>
			<artifactId>TokenManager</artifactId>
			<version>3.2.4</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>me.TechsCode</groupId>
			<artifactId>UltraEconomyAPI</artifactId>
			<version>1.1.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>su.nightexpress.coinsengine</groupId>
			<artifactId>CoinsEngine</artifactId>
			<version>2.4.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>me.clip</groupId>
			<artifactId>placeholderapi</artifactId>
			<version>2.11.5</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.bgsoftware</groupId>
			<artifactId>WildStackerAPI</artifactId>
			<version>2023.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.craftaro</groupId>
			<artifactId>UltimateStacker-API</artifactId>
			<version>1.0.0-20240329.173606-35</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>dev.rosewood</groupId>
			<artifactId>rosestacker</artifactId>
			<version>1.5.20</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>io.github.projectunified</groupId>
			<artifactId>uni-item-all</artifactId>
			<version>2.2.1</version>
		</dependency>

		<dependency>
			<groupId>me.hsgamer</groupId>
			<artifactId>hscore-database-driver-sqlite</artifactId>
			<version>${core.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.xerial</groupId>
					<artifactId>sqlite-jdbc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>me.hsgamer</groupId>
			<artifactId>hscore-database-driver-mysql</artifactId>
			<version>${core.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.mysql</groupId>
					<artifactId>mysql-connector-j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>me.hsgamer</groupId>
			<artifactId>hscore-database-client-java</artifactId>
			<version>${core.version}</version>
		</dependency>
		<dependency>
			<groupId>me.hsgamer</groupId>
			<artifactId>hscore-web</artifactId>
			<version>${core.version}</version>
		</dependency>
	</dependencies>
	
</project>
