package shyrcs.Ability;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

public class BuffEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    private final Player player;
    private final PotionEffectType buffType;
    private final int duration;
    private final int amplifier;
    private final String clickType;
    private final int cooldown;
    private final double buffValue;
    
    public BuffEvent(Player player, PotionEffectType buffType, int duration, int amplifier, 
                     String clickType, int cooldown, double buffValue) {
        this.player = player;
        this.buffType = buffType;
        this.duration = duration;
        this.amplifier = amplifier;
        this.clickType = clickType;
        this.cooldown = cooldown;
        this.buffValue = buffValue;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public PotionEffectType getBuffType() {
        return buffType;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public int getAmplifier() {
        return amplifier;
    }
    
    public String getClickType() {
        return clickType;
    }
    
    public int getCooldown() {
        return cooldown;
    }
    
    public double getBuffValue() {
        return buffValue;
    }
    
    @Override
    public HandlerList getHandlers() {
        return handlers;
    }
    
    public static HandlerList getHandlerList() {
        return handlers;
    }
}
