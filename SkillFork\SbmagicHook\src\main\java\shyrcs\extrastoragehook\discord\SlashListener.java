package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.entities.channel.ChannelType;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Listener cho slash commands
 */
public class SlashListener extends ListenerAdapter {
    
    @Override
    public void onSlashCommandInteraction(final SlashCommandInteractionEvent event) {
        // Kiểm tra slash commands có được bật không
        if (!Library.config.useSlashCommands()) {
            return;
        }
        
        // Chỉ xử lý trong text channels và kiểm tra permissions
        if (!event.getChannel().getType().equals(ChannelType.TEXT) || !verifyInteraction(event)) {
            return;
        }
        
        MessageChannel channel = event.getChannel();
        
        // Kiểm tra channel restrictions
        if (BotSetup.isLimited()) {
            if (!BotSetup.getWhitelistedChannels().contains(channel.getId())) {
                event.reply(Library.config.getMessage("not-permitted"))
                    .setEphemeral(true).queue();
                return;
            }
        }
        
        try {
            // Xử lý command nếu tồn tại
            if (Library.manager.hasCommands(event.getName())) {
                new BukkitRunnable() {
                    public void run() {
                        try {
                            Library.manager.getCommand(event.getName()).onSlashCommand(event);
                        } catch (Exception e) {
                            SbMagicHook.error("Lỗi khi xử lý slash command: " + e.getMessage());
                            e.printStackTrace();
                            
                            if (!event.isAcknowledged()) {
                                event.reply(Library.config.getMessage("error"))
                                    .setEphemeral(true).queue();
                            }
                        }
                    }
                }.runTaskAsynchronously(PluginBoot.main);
            }
        } catch (Exception err) { 
            SbMagicHook.error("Lỗi trong SlashListener: " + err.getMessage());
            err.printStackTrace();
        }
    }
    
    /**
     * Xác minh interaction
     */
    private boolean verifyInteraction(SlashCommandInteractionEvent event) {
        // Kiểm tra guild
        if (event.getGuild() == null) {
            return false;
        }
        
        // Kiểm tra guild ID
        String guildId = Library.config.getGuildID();
        if (!guildId.isEmpty() && !event.getGuild().getId().equals(guildId)) {
            return false;
        }
        
        // Kiểm tra member
        if (event.getMember() == null) {
            return false;
        }
        
        return true;
    }
}
