package shyrcs.Ability;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import io.papermc.paper.event.player.AsyncChatEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.event.block.Action;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class BuffListener implements Listener {

    private final Map<UUID, Boolean> waitingForInput = new HashMap<>();
    private CooldownManager cooldownManager;

    public BuffListener(org.bukkit.plugin.Plugin plugin) {
        this.cooldownManager = CooldownManager.getInstance(plugin);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return;
        }
        
        // Kiểm tra xem item có phải là buff item không (có thể customize logic này)
        String itemName = item.getItemMeta().displayName().toString();
        if (!itemName.contains("Buff") && !itemName.contains("buff")) {
            return;
        }
        
        Action action = event.getAction();

        // Chỉ xử lý left click và right click
        if (!(action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK ||
              action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK)) {
            return;
        }

        // Xác định loại click (bao gồm shift)
        String clickType = "";
        boolean isShiftClick = player.isSneaking();

        if (action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK) {
            clickType = isShiftClick ? "shift_left_click" : "left_click";
        } else if (action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK) {
            clickType = isShiftClick ? "shift_right_click" : "right_click";
        }
        
        // Kiểm tra cooldown sử dụng CooldownManager
        if (cooldownManager.isOnCooldown(player, "buff")) {
            long remainingTime = cooldownManager.getRemainingCooldown(player, "buff");
            String cooldownMessage = "§c⏱ Buff Cooldown: " + BuffUtils.formatCooldownTime(remainingTime);
            BuffUtils.sendActionBar(player, cooldownMessage);
            event.setCancelled(true);
            return;
        }

        // Đặt player vào trạng thái chờ input
        UUID playerId = player.getUniqueId();
        waitingForInput.put(playerId, true);
        player.sendMessage("§aNhập thông tin buff theo format:");
        player.sendMessage("§e<buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
        player.sendMessage("§eVí dụ: absorption 2 10 left_click 5");
        player.sendMessage("§7Click type hiện tại: §e" + clickType);
        
        event.setCancelled(true);
    }
    
    @EventHandler
    public void onPlayerChat(AsyncChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        if (!waitingForInput.getOrDefault(playerId, false)) {
            return;
        }

        event.setCancelled(true);
        waitingForInput.remove(playerId);

        // Lấy message từ Component
        String message = "";
        try {
            message = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText()
                    .serialize(event.message()).trim();
        } catch (Exception e) {
            // Fallback nếu không thể serialize
            message = event.message().toString().trim();
        }
        String[] parts = message.split(" ");

        // Kiểm tra số lượng tham số
        if (parts.length != 5) {
            player.sendMessage("§cFormat không đúng! Sử dụng: <buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
            player.sendMessage("§eVí dụ: absorption 2 10 left_click 5");
            return;
        }

        // Kiểm tra không có tham số nào bị trống
        for (int i = 0; i < parts.length; i++) {
            if (parts[i] == null || parts[i].trim().isEmpty()) {
                player.sendMessage("§cTham số thứ " + (i + 1) + " không được để trống!");
                player.sendMessage("§eFormat: <buff_id> <level_buff> <time_buff> <click_type> <cooldown>");
                return;
            }
            parts[i] = parts[i].trim(); // Loại bỏ khoảng trắng thừa
        }

        try {
            String buffId = parts[0].toUpperCase();
            int levelBuff = Integer.parseInt(parts[1]);
            int timeBuff = Integer.parseInt(parts[2]);
            String clickType = parts[3].toLowerCase();
            int cooldown = Integer.parseInt(parts[4]);

            // Validate các giá trị số phải dương
            if (levelBuff <= 0) {
                player.sendMessage("§cLevel buff phải là số dương!");
                return;
            }

            if (timeBuff <= 0) {
                player.sendMessage("§cThời gian buff phải là số dương!");
                return;
            }

            if (cooldown < 0) {
                player.sendMessage("§cCooldown không được âm!");
                return;
            }
            
            // Validate level buff
            if (!BuffUtils.isValidLevel(levelBuff)) {
                player.sendMessage("§cLevel buff phải từ 1 đến 256!");
                return;
            }

            // Validate click type
            if (!BuffUtils.isValidClickType(clickType)) {
                player.sendMessage("§cClick type không hợp lệ!");
                player.sendMessage("§eClick types hỗ trợ: §fleft_click, right_click, shift_left_click, shift_right_click");
                return;
            }

            // Validate buff type
            PotionEffectType buffType = BuffUtils.getEffectTypeByName(buffId);
            if (buffType == null) {
                player.sendMessage("§cBuff ID không hợp lệ: " + buffId);
                player.sendMessage("§7Gõ '/buffs' để xem danh sách đầy đủ!");
                return;
            }
            
            // Áp dụng buff với level
            applyBuff(player, buffType, levelBuff, timeBuff, clickType, cooldown);

        } catch (NumberFormatException e) {
            player.sendMessage("§cLỗi format số! Kiểm tra lại:");
            player.sendMessage("§e- Level buff: phải là số nguyên (1-256)");
            player.sendMessage("§e- Time buff: phải là số nguyên dương (giây)");
            player.sendMessage("§e- Cooldown: phải là số nguyên không âm (giây)");
            player.sendMessage("§7Ví dụ: absorption 2 10 left_click 5");
        }
    }
    
    private void applyBuff(Player player, PotionEffectType buffType, int level, int duration, String clickType, int cooldown) {
        // Chuyển level thành amplifier sử dụng BuffUtils
        int amplifier = BuffUtils.levelToAmplifier(level);

        // Tạo potion effect với level
        PotionEffect effect = new PotionEffect(buffType, duration * 20, amplifier);
        player.addPotionEffect(effect);

        // Đặt cooldown sử dụng CooldownManager
        cooldownManager.setCooldown(player, "buff", cooldown);

        // Gửi thông báo với tên tiếng Việt
        String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(buffType);
        String effectName = BuffUtils.getEffectTypeName(buffType);
        String levelRoman = BuffUtils.toRoman(level);
        player.sendMessage("§aĐã áp dụng buff " + buffNameVi + " " + levelRoman + " (" + effectName + ")");
        player.sendMessage("§eThời gian: " + duration + " giây | Click: " + clickType + " | Cooldown: " + cooldown + "s");

        // Tạo và gọi BuffEvent
        BuffEvent buffEvent = new BuffEvent(player, buffType, duration, amplifier, clickType, cooldown, level);
        Bukkit.getPluginManager().callEvent(buffEvent);
    }
}
