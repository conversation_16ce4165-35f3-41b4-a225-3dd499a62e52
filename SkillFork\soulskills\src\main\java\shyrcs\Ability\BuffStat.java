package shyrcs.Ability;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.potion.PotionEffectType;

public class BuffStat {
    private final PotionEffectType buffType;
    private final int duration;
    private final int amplifier;
    private final String clickType;
    private final int cooldown;
    private final double buffValue;
    
    public BuffStat(ConfigurationSection config) {
        String buffId = config.getString("buff-id", "ABSORPTION");
        this.buffType = BuffUtils.getEffectTypeByName(buffId);
        this.duration = config.getInt("duration", 10);
        // Hỗ trợ cả level và amplifier (level = amplifier + 1)
        int level = config.getInt("level", 1);
        this.amplifier = config.getInt("amplifier", level - 1);
        this.clickType = config.getString("click-type", "right_click");
        this.cooldown = config.getInt("cooldown", 5);
        this.buffValue = config.getDouble("buff-value", 1.0);
    }
    
    public BuffStat(PotionEffectType buffType, int duration, int amplifier, String clickType, int cooldown, double buffValue) {
        this.buffType = buffType;
        this.duration = duration;
        this.amplifier = amplifier;
        this.clickType = clickType;
        this.cooldown = cooldown;
        this.buffValue = buffValue;
    }
    
    public PotionEffectType getBuffType() {
        return buffType;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public int getAmplifier() {
        return amplifier;
    }

    public int getLevel() {
        return amplifier + 1;
    }
    
    public String getClickType() {
        return clickType;
    }
    
    public int getCooldown() {
        return cooldown;
    }
    
    public double getBuffValue() {
        return buffValue;
    }
    
    public boolean isValidBuff() {
        return buffType != null;
    }
    
    @Override
    public String toString() {
        return String.format("BuffStat{type=%s, duration=%d, level=%d, amplifier=%d, click=%s, cooldown=%d, value=%.2f}",
                buffType != null ? BuffUtils.getEffectTypeName(buffType) : "INVALID",
                duration, getLevel(), amplifier, clickType, cooldown, buffValue);
    }
}
