[17:31:10] [ServerMain/INFO]: [bootstrap] Running Java 22 (OpenJDK 64-Bit Server VM 22.0.1+8; Eclipse Adoptium Temurin-22.0.1+8) on Linux 5.15.0-141-generic (amd64)
[17:31:10] [ServerMain/INFO]: [bootstrap] Loading Leaf 1.21.4-496-ver/1.21.4@5311ae8 (2025-06-20T17:12:29Z) for Minecraft 1.21.4
[17:31:10] [ServerMain/INFO]: [LeafConfig] Loading config...
[17:31:11] [ServerMain/INFO]: [LeafConfig] Successfully loaded config in 42ms.
[17:31:11] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[17:31:11] [ServerMain/ERROR]: [PluginRemapper] Encountered exception remapping plugins
java.util.concurrent.CompletionException: java.lang.RuntimeException: Failed to open plugin jar plugins/TitleManager-2.3.6.jar
	at java.base/java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:413) ~[?:?]
	at java.base/java.util.concurrent.CompletableFuture.join(CompletableFuture.java:2118) ~[?:?]
	at io.papermc.paper.pluginremap.PluginRemapper.waitForAll(PluginRemapper.java:410) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.pluginremap.PluginRemapper.rewritePluginDirectory(PluginRemapper.java:206) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.provider.source.DirectoryProviderSource.prepareContext(DirectoryProviderSource.java:42) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.provider.source.DirectoryProviderSource.prepareContext(DirectoryProviderSource.java:17) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.util.EntrypointUtil.registerProvidersFromSource(EntrypointUtil.java:14) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.PluginInitializerManager.load(PluginInitializerManager.java:113) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.Main.main(Main.java:118) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.PaperBootstrap.boot(PaperBootstrap.java:21) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.dreeam.leaf.LeafBootstrap.boot(LeafBootstrap.java:13) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.Main.main(Main.java:289) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at cn.dreeam.leaper.QuantumLeaper.lambda$main$0(QuantumLeaper.java:41) ~[server.jar:?]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
Caused by: java.lang.RuntimeException: Failed to open plugin jar plugins/TitleManager-2.3.6.jar
	at io.papermc.paper.pluginremap.PluginRemapper.remap(PluginRemapper.java:339) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.pluginremap.PluginRemapper.remapPlugin(PluginRemapper.java:266) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.pluginremap.PluginRemapper.rewritePluginDirectory(PluginRemapper.java:204) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	... 10 more
Caused by: java.util.zip.ZipException: zip END header not found
	at jdk.zipfs@22.0.1/jdk.nio.zipfs.ZipFileSystem.findEND(ZipFileSystem.java:1331) ~[jdk.zipfs:?]
	at jdk.zipfs@22.0.1/jdk.nio.zipfs.ZipFileSystem.initCEN(ZipFileSystem.java:1552) ~[jdk.zipfs:?]
	at jdk.zipfs@22.0.1/jdk.nio.zipfs.ZipFileSystem.<init>(ZipFileSystem.java:179) ~[jdk.zipfs:?]
	at jdk.zipfs@22.0.1/jdk.nio.zipfs.ZipFileSystemProvider.getZipFileSystem(ZipFileSystemProvider.java:125) ~[jdk.zipfs:?]
	at jdk.zipfs@22.0.1/jdk.nio.zipfs.ZipFileSystemProvider.newFileSystem(ZipFileSystemProvider.java:120) ~[jdk.zipfs:?]
	at java.base/java.nio.file.FileSystems.newFileSystem(FileSystems.java:528) ~[?:?]
	at java.base/java.nio.file.FileSystems.newFileSystem(FileSystems.java:440) ~[?:?]
	at io.papermc.paper.pluginremap.PluginRemapper.remap(PluginRemapper.java:290) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.pluginremap.PluginRemapper.remapPlugin(PluginRemapper.java:266) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.pluginremap.PluginRemapper.rewritePluginDirectory(PluginRemapper.java:204) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	... 10 more
[17:31:12] [ServerMain/INFO]: [PluginInitializerManager] Initialized 118 plugins
[17:31:12] [ServerMain/INFO]: [PluginInitializerManager] Paper plugins (3):
 - FancyHolograms (2.4.2), RoseStacker (1.5.33), nightcore (*******)
[17:31:12] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (115):
 - AdvancedChests (43.5), AdvancedOreGen (1.6.69-SNAPSHOT), AuraSkills (2.3.3), AxEnvoy (2.0.6), AxTrade (1.17.0), AxVaults (2.9.0), Backuper (3.4.1), BattlePass (4.9.15), BeaconPlus3 (3.1.0), BeeMinions (5.0.2-BETA), BetterFarming (5.10.4), Citizens (2.0.37-SNAPSHOT (build 3760)), ClansPlus (1.7), CoinsEngine (2.4.2), ConsoleSpamFixReborn (1.11.5), CoreTools (1.2-SNAPSHOT), CustomCrops (3.6.40), CustomOres (6.5), DecentHolograms (2.8.17), DeluxeBazaar (9.9), DeluxeCoinflip (2.9.6), DeluxeMenus (1.14.0-Release), DeluxeTags (1.8.2-Release), DiscordSRV (1.29.0), EcoPets (2.75.1), EpicCraftingsPlus (7.33.1), Essentials (2.21.0), EssentialsSpawn (2.21.0), ExcellentCrates (6.2.2), ExecutableEvents (*********), ExploitFixer (3.2.1), ExtraStorage (1.0), FastAsyncWorldEdit (2.13.1-SNAPSHOT-1075;fdc9d6d), FeatheredElection (5.0.4-BETA), FreedomChat (1.7.2), GSit (2.3.2), GiftCode24 (2.1.0-Stable), HamsterAPI (0.2.5), HeadBlocks (2.6.15), HeadDatabase (4.21.2), InsaneAnnouncer (1.4.5), InteractiveChat (*******), InteractiveChatDiscordSrvAddon (*******), InvSeePlusPlus (0.29.23-SNAPSHOT), InvSeePlusPlus_Clear (0.29.23-SNAPSHOT), InvSeePlusPlus_Give (0.29.23-SNAPSHOT), InventoryRollbackPlus (1.7.0), ItemEdit (3.7.0), ItemsAdder (4.0.11), LPC (3.6.1), LPX (3.5.12), LibsDisguises (11.0.5), LitLibs (1.1.32), LitMinions (4.4.6), LiteBans (2.17.2), LiteSignIn (*******), LiteSignIn (*******), LoneLibs (1.0.65), LuckPerms (5.4.158), MMDiscordNotifs (1.1), MMOInventory (2.0-SNAPSHOT), MMOItems (6.10.1-SNAPSHOT), ModelEngine (R4.0.9), Multiverse-Core (4.3.16), MyCommand (5.7.4), MythicAnnouncer (1.5.0), MythicCrucible (2.2.0-SNAPSHOT), MythicDungeons (2.0.1-SNAPSHOT), MythicLib (1.7.1-SNAPSHOT), MythicMobs (5.9.2-SNAPSHOT-538a490c), NBTAPI (2.14.1), Orestack (3.28.1), PandeLoot (dev), PhoBan (1.0.0), PhoBanPro (1.2.11), PlaceholderAPI (2.11.6), PlayerAuctions (1.31.1), PlayerData (1.2), PlayerPoints (3.3.0), PlaytimeRewardsPlus (1.1.1), PlugManX (2.4.1), ProtocolLib (5.4.0-SNAPSHOT-742), Quests (5.2.2-b533), ReplenishEnchant (1.1), RoseLoot (1.3.0), SCore (*********), SafeNET (4.0), SbmagicTop (1.0.0), ShopGUIPlus (1.106.1), Shopkeepers (2.23.8), SimpPay (1.2.4-BETA), SkinsRestorer (15.7.7), Skript (2.11.2), SoulSkills (1.0), SuperVanish (6.2.20), SuperiorSkyblock2 (2024.4-b445), TAB (5.2.0), TaiXiu (2.6), UltimateKoth (2.13.0), UltraBar (*******), Vault (1.7.3-b131), ViaBackwards (5.3.2-SNAPSHOT), ViaRewind (4.0.7), ViaVersion (5.3.2-SNAPSHOT), VouchersPlus (1.1.8), Vulcan (2.9.5), WildTools (2025.1), WorldGuard (7.0.13+82fdc65), WorldGuardExtraFlags (4.2.4-SNAPSHOT), ajLeaderboards (2.8.0), antiRedstoneClock (1.5.0), floodgate (2.2.4-SNAPSHOT (b116-0e3163c)), packetevents (2.8.0), skript-placeholders (1.7.0), zMenu (*******)
[17:31:15] [ServerMain/ERROR]: You have used the Spigot command line EULA agreement flag.
[17:31:15] [ServerMain/ERROR]: By using this setting you are indicating your agreement to Mojang's EULA (https://aka.ms/MinecraftEULA).
[17:31:15] [ServerMain/ERROR]: If you do not agree to the above EULA please stop your server and remove this flag immediately.
[17:31:15] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[17:31:15] [ServerMain/INFO]: Loaded 1370 recipes
[17:31:15] [ServerMain/INFO]: Loaded 1481 advancements
[17:31:16] [ServerMain/INFO]: [MCTypeRegistry] Initialising converters for DataConverter...
[17:31:16] [ServerMain/INFO]: [MCTypeRegistry] Finished initialising converters for DataConverter in 124.3ms
[17:31:16] [Server thread/INFO]: Starting minecraft server version 1.21.4
[17:31:16] [Server thread/INFO]: Loading properties
[17:31:16] [Server thread/INFO]: This server is running Leaf version 1.21.4-496-ver/1.21.4@5311ae8 (2025-06-20T17:12:29Z) (Implementing API version 1.21.4-R0.1-SNAPSHOT)
[17:31:16] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[17:31:16] [Server thread/INFO]: Server Ping Player Sample Count: 12
[17:31:16] [Server thread/INFO]: Using 4 threads for Netty based IO
[17:31:16] [Server thread/INFO]: [MoonriseCommon] Paper is using 1 worker threads, 1 I/O threads
[17:31:16] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[17:31:16] [Server thread/INFO]: Default game type: SURVIVAL
[17:31:16] [Server thread/INFO]: Generating keypair
[17:31:16] [Server thread/INFO]: Starting Minecraft server on 0.0.0.0:30001
[17:31:16] [Server thread/INFO]: Using epoll channel type
[17:31:16] [Server thread/INFO]: Paper: Using libdeflate (Linux x86_64) compression from Velocity.
[17:31:16] [Server thread/INFO]: Paper: Using OpenSSL 3.x.x (Linux x86_64) cipher from Velocity.
[17:31:16] [Server thread/ERROR]: [LegacyPluginLoadingStrategy] Ambiguous plugin name `LiteSignIn' for files `plugins/.paper-remapped/LiteSignIn-*******.jar' and `plugins/.paper-remapped/LiteSignIn-*******.jar' in `plugins/.paper-remapped'
[17:31:17] [Server thread/INFO]: [SpigotLibraryLoader] [LoneLibs] Loading 1 libraries... please wait
[17:31:17] [Server thread/INFO]: [SpigotLibraryLoader] [LoneLibs] Loaded library /home/<USER>/libraries/org/fusesource/jansi/jansi/2.4.1/jansi-2.4.1.jar
[17:31:17] [Server thread/ERROR]: [LegacyPluginLoadingStrategy] Could not load 'plugins/.paper-remapped/EcoPets v2.75.1.jar' in folder 'plugins/.paper-remapped'
org.bukkit.plugin.UnknownDependencyException: Unknown/missing dependency plugins: [eco]. Please download and install these plugins to run 'EcoPets'.
	at io.papermc.paper.plugin.entrypoint.strategy.LegacyPluginLoadingStrategy.loadProviders(LegacyPluginLoadingStrategy.java:169) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.storage.SimpleProviderStorage.enter(SimpleProviderStorage.java:38) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.entrypoint.LaunchEntryPointHandler.enter(LaunchEntryPointHandler.java:39) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.CraftServer.loadPlugins(CraftServer.java:582) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:344) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
[17:31:21] [Server thread/WARN]: Legacy plugin PlayerData v1.2 does not specify an api-version.
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [PlayerPoints] Loading 2 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [PlayerPoints] Loaded library /home/<USER>/libraries/com/mysql/mysql-connector-j/9.1.0/mysql-connector-j-9.1.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [PlayerPoints] Loaded library /home/<USER>/libraries/com/google/protobuf/protobuf-java/4.26.1/protobuf-java-4.26.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [PlayerPoints] Loaded library /home/<USER>/libraries/org/xerial/sqlite-jdbc/3.46.0.0/sqlite-jdbc-3.46.0.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [PlayerPoints] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loading 4 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loaded library /home/<USER>/libraries/com/h2database/h2/2.3.232/h2-2.3.232.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loaded library /home/<USER>/libraries/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loaded library /home/<USER>/libraries/com/j256/ormlite/ormlite-jdbc/6.1/ormlite-jdbc-6.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SimpPay] Loaded library /home/<USER>/libraries/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar
[17:31:21] [Server thread/WARN]: [nightcore] Loading Paper plugin in the legacy plugin loading logic. This is not recommended and may introduce some differences into load order. It's highly recommended you move away from this if you are wanting to use Paper plugins.
[17:31:21] [Server thread/WARN]: [FancyHolograms] Loading Paper plugin in the legacy plugin loading logic. This is not recommended and may introduce some differences into load order. It's highly recommended you move away from this if you are wanting to use Paper plugins.
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loading 7 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/com/h2database/h2/2.3.232/h2-2.3.232.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/com/zaxxer/HikariCP/5.1.0/HikariCP-5.1.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/io/github/pigaut/sql/SQLib/1.4.1/SQLib-1.4.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/org/snakeyaml/snakeyaml-engine/2.9/snakeyaml-engine-2.9.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/io/github/pigaut/yaml/YamlConfig/3.4.8/YamlConfig-3.4.8.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/io/github/pigaut/yaml/SpigotYamlConfig/3.4.6/SpigotYamlConfig-3.4.6.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Orestack] Loaded library /home/<USER>/libraries/com/github/cryptomorin/XSeries/13.2.0/XSeries-13.2.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loading 5 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/ch/ethz/globis/phtree/phtree/2.8.1/phtree-2.8.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/org/joml/joml/1.10.8/joml-1.10.8.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/it/unimi/dsi/fastutil/8.5.15/fastutil-8.5.15.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-bukkit/4.3.3/adventure-platform-bukkit-4.3.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-api/4.3.3/adventure-platform-api-4.3.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.3/adventure-text-serializer-bungeecord-4.3.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-legacy/4.13.1/adventure-text-serializer-legacy-4.13.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-nbt/4.13.1/adventure-nbt-4.13.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/24.0.1/annotations-24.0.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson/4.13.1/adventure-text-serializer-gson-4.13.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson-legacy-impl/4.13.1/adventure-text-serializer-gson-legacy-impl-4.13.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-facet/4.3.3/adventure-platform-facet-4.3.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-viaversion/4.3.3/adventure-platform-viaversion-4.3.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-minimessage/4.17.0/adventure-text-minimessage-4.17.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-api/4.17.0/adventure-api-4.17.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [Citizens] Loaded library /home/<USER>/libraries/net/kyori/adventure-key/4.17.0/adventure-key-4.17.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [BetterFarming] Loading 4 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [BetterFarming] Loaded library /home/<USER>/libraries/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [BetterFarming] Loaded library /home/<USER>/libraries/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [BetterFarming] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-jdk14/1.7.32/slf4j-jdk14-1.7.32.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [BetterFarming] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loading 1 libraries... please wait
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/openjdk/nashorn/nashorn-core/15.4/nashorn-core-15.4.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/ow2/asm/asm/7.3.1/asm-7.3.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/ow2/asm/asm-commons/7.3.1/asm-commons-7.3.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/ow2/asm/asm-analysis/7.3.1/asm-analysis-7.3.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/ow2/asm/asm-tree/7.3.1/asm-tree-7.3.1.jar
[17:31:21] [Server thread/INFO]: [SpigotLibraryLoader] [SuperiorSkyblock2] Loaded library /home/<USER>/libraries/org/ow2/asm/asm-util/7.3.1/asm-util-7.3.1.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loading 1 libraries... please wait
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/lettuce/lettuce-core/6.5.3.RELEASE/lettuce-core-6.5.3.RELEASE.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-common/4.1.115.Final/netty-common-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-handler/4.1.115.Final/netty-handler-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-resolver/4.1.115.Final/netty-resolver-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-buffer/4.1.115.Final/netty-buffer-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-unix-common/4.1.115.Final/netty-transport-native-unix-common-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-codec/4.1.115.Final/netty-codec-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/netty/netty-transport/4.1.115.Final/netty-transport-4.1.115.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/io/projectreactor/reactor-core/3.6.6/reactor-core-3.6.6.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [CoreTools] Loaded library /home/<USER>/libraries/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loading 1 libraries... please wait
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-all/4.1.118.Final/netty-all-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-buffer/4.1.118.Final/netty-buffer-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec/4.1.118.Final/netty-codec-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-dns/4.1.118.Final/netty-codec-dns-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-haproxy/4.1.118.Final/netty-codec-haproxy-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-http/4.1.118.Final/netty-codec-http-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-http2/4.1.118.Final/netty-codec-http2-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-memcache/4.1.118.Final/netty-codec-memcache-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-mqtt/4.1.118.Final/netty-codec-mqtt-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-redis/4.1.118.Final/netty-codec-redis-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-smtp/4.1.118.Final/netty-codec-smtp-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-socks/4.1.118.Final/netty-codec-socks-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-stomp/4.1.118.Final/netty-codec-stomp-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-codec-xml/4.1.118.Final/netty-codec-xml-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-common/4.1.118.Final/netty-common-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-handler/4.1.118.Final/netty-handler-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-unix-common/4.1.118.Final/netty-transport-native-unix-common-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-handler-proxy/4.1.118.Final/netty-handler-proxy-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-handler-ssl-ocsp/4.1.118.Final/netty-handler-ssl-ocsp-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-resolver/4.1.118.Final/netty-resolver-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-resolver-dns/4.1.118.Final/netty-resolver-dns-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport/4.1.118.Final/netty-transport-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-rxtx/4.1.118.Final/netty-transport-rxtx-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-sctp/4.1.118.Final/netty-transport-sctp-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-udt/4.1.118.Final/netty-transport-udt-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-classes-epoll/4.1.118.Final/netty-transport-classes-epoll-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-classes-kqueue/4.1.118.Final/netty-transport-classes-kqueue-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-resolver-dns-classes-macos/4.1.118.Final/netty-resolver-dns-classes-macos-4.1.118.Final.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-epoll/4.1.118.Final/netty-transport-native-epoll-4.1.118.Final-linux-x86_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-epoll/4.1.118.Final/netty-transport-native-epoll-4.1.118.Final-linux-aarch_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-epoll/4.1.118.Final/netty-transport-native-epoll-4.1.118.Final-linux-riscv64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-kqueue/4.1.118.Final/netty-transport-native-kqueue-4.1.118.Final-osx-x86_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-transport-native-kqueue/4.1.118.Final/netty-transport-native-kqueue-4.1.118.Final-osx-aarch_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-resolver-dns-native-macos/4.1.118.Final/netty-resolver-dns-native-macos-4.1.118.Final-osx-x86_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [SCore] Loaded library /home/<USER>/libraries/io/netty/netty-resolver-dns-native-macos/4.1.118.Final/netty-resolver-dns-native-macos-4.1.118.Final-osx-aarch_64.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loading 4 libraries... please wait
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-api/4.16.0/adventure-api-4.16.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-key/4.16.0/adventure-key-4.16.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/24.1.0/annotations-24.1.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-bukkit/4.3.2/adventure-platform-bukkit-4.3.2.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-api/4.3.2/adventure-platform-api-4.3.2.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.2/adventure-text-serializer-bungeecord-4.3.2.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-legacy/4.13.1/adventure-text-serializer-legacy-4.13.1.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-nbt/4.13.1/adventure-nbt-4.13.1.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson-legacy-impl/4.13.1/adventure-text-serializer-gson-legacy-impl-4.13.1.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-facet/4.3.2/adventure-platform-facet-4.3.2.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-viaversion/4.3.2/adventure-platform-viaversion-4.3.2.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-minimessage/4.16.0/adventure-text-minimessage-4.16.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson/4.16.0/adventure-text-serializer-gson-4.16.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-json/4.16.0/adventure-text-serializer-json-4.16.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/net/kyori/option/1.0.0/option-1.0.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicMobs] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.8.0/gson-2.8.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [BattlePass] Loading 3 libraries... please wait
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [BattlePass] Loaded library /home/<USER>/libraries/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [BattlePass] Loaded library /home/<USER>/libraries/com/zaxxer/HikariCP/3.4.5/HikariCP-3.4.5.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [BattlePass] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loading 1 libraries... please wait
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/club/minnced/discord-webhooks/0.8.4/discord-webhooks-0.8.4.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/com/squareup/okhttp3/okhttp/4.10.0/okhttp-4.10.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/com/squareup/okio/okio-jvm/3.0.0/okio-jvm-3.0.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.5.31/kotlin-stdlib-jdk8-1.5.31.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.5.31/kotlin-stdlib-jdk7-1.5.31.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-common/1.5.31/kotlin-stdlib-common-1.5.31.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib/1.6.20/kotlin-stdlib-1.6.20.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/13.0/annotations-13.0.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [MythicAnnouncer] Loaded library /home/<USER>/libraries/org/json/json/20230618/json-20230618.jar
[17:31:22] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loading 3 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-api/4.14.0/adventure-api-4.14.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-key/4.14.0/adventure-key-4.14.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/24.0.1/annotations-24.0.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson/4.14.0/adventure-text-serializer-gson-4.14.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-json/4.14.0/adventure-text-serializer-json-4.14.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.8.0/gson-2.8.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.0/adventure-text-serializer-bungeecord-4.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ModelEngine] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-legacy/4.13.0/adventure-text-serializer-legacy-4.13.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loading 1 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/club/minnced/discord-webhooks/0.8.2/discord-webhooks-0.8.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/com/squareup/okhttp3/okhttp/4.10.0/okhttp-4.10.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/com/squareup/okio/okio-jvm/3.0.0/okio-jvm-3.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.5.31/kotlin-stdlib-jdk8-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.5.31/kotlin-stdlib-jdk7-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-common/1.5.31/kotlin-stdlib-common-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib/1.6.20/kotlin-stdlib-1.6.20.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/json/json/20210307/json-20210307.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [MMDiscordNotifs] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/22.0.0/annotations-22.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loading 1 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loaded library /home/<USER>/libraries/redis/clients/jedis/5.1.3/jedis-5.1.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loaded library /home/<USER>/libraries/org/apache/commons/commons-pool2/2.12.0/commons-pool2-2.12.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loaded library /home/<USER>/libraries/org/json/json/20231013/json-20231013.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [BeeMinions] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loading 14 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/beer/devs/FastNbt-jar/1.4.8/FastNbt-jar-1.4.8.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/apache/httpcomponents/httpmime/4.5.14/httpmime-4.5.14.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/commons-logging/commons-logging/1.2/commons-logging-1.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/commons-codec/commons-codec/1.11/commons-codec-1.11.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/sourceforge/streamsupport/speedy-math/1.0.0/speedy-math-1.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/fusesource/jansi/jansi/2.4.1/jansi-2.4.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-api/4.20.0/adventure-api-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-key/4.20.0/adventure-key-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/26.0.2/annotations-26.0.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson/4.20.0/adventure-text-serializer-gson-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-json/4.20.0/adventure-text-serializer-json-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/option/1.1.0/option-1.1.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.8.0/gson-2.8.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/google/auto/service/auto-service-annotations/1.1.1/auto-service-annotations-1.1.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-commons/4.20.0/adventure-text-serializer-commons-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson-legacy-impl/4.20.0/adventure-text-serializer-gson-legacy-impl-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-json-legacy-impl/4.20.0/adventure-text-serializer-json-legacy-impl-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-minimessage/4.20.0/adventure-text-minimessage-4.20.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-bukkit/4.3.4/adventure-platform-bukkit-4.3.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-api/4.3.4/adventure-platform-api-4.3.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.4/adventure-text-serializer-bungeecord-4.3.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-legacy/4.13.1/adventure-text-serializer-legacy-4.13.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-nbt/4.13.1/adventure-nbt-4.13.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-facet/4.3.4/adventure-platform-facet-4.3.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-viaversion/4.3.4/adventure-platform-viaversion-4.3.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/alibaba/fastjson/2.0.43/fastjson-2.0.43.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/alibaba/fastjson2/fastjson2-extension/2.0.43/fastjson2-extension-2.0.43.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/alibaba/fastjson2/fastjson2/2.0.43/fastjson2-2.0.43.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/minidev/json-smart/2.4.10/json-smart-2.4.10.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/net/minidev/accessors-smart/2.4.9/accessors-smart-2.4.9.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/ow2/asm/asm/9.3/asm-9.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/com/jeff-media/armor-equip-event/1.0.3/armor-equip-event-1.0.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/fr/skytasul/glowingentities/1.4.3/glowingentities-1.4.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemsAdder] Loaded library /home/<USER>/libraries/fr/skytasul/reflection-remapper/1.0.0/reflection-remapper-1.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loading 4 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/github/ben-manes/caffeine/caffeine/3.0.2/caffeine-3.0.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/google/errorprone/error_prone_annotations/2.6.0/error_prone_annotations-2.6.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/redis/clients/jedis/4.3.0/jedis-4.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/json/json/20220320/json-20220320.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.8.9/gson-2.8.9.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/net/dv8tion/JDA/5.0.0-beta.18/JDA-5.0.0-beta.18.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/neovisionaries/nv-websocket-client/2.14/nv-websocket-client-2.14.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/squareup/okhttp3/okhttp/4.10.0/okhttp-4.10.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/squareup/okio/okio-jvm/3.0.0/okio-jvm-3.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.5.31/kotlin-stdlib-jdk8-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.5.31/kotlin-stdlib-jdk7-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib-common/1.5.31/kotlin-stdlib-common-1.5.31.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/jetbrains/kotlin/kotlin-stdlib/1.6.20/kotlin-stdlib-1.6.20.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/13.0/annotations-13.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/club/minnced/opus-java/1.1.1/opus-java-1.1.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/club/minnced/opus-java-api/1.1.1/opus-java-api-1.1.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/net/java/dev/jna/jna/4.4.0/jna-4.4.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/club/minnced/opus-java-natives/1.1.1/opus-java-natives-1.1.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/net/sf/trove4j/trove4j/3.0.3/trove4j-3.0.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/fasterxml/jackson/core/jackson-core/2.14.1/jackson-core-2.14.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/fasterxml/jackson/core/jackson-databind/2.14.1/jackson-databind-2.14.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/com/fasterxml/jackson/core/jackson-annotations/2.14.1/jackson-annotations-2.14.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [FeatheredElection] Loaded library /home/<USER>/libraries/org/mariuszgromada/math/MathParser.org-mXparser/4.4.2/MathParser.org-mXparser-4.4.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loading 5 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-api/4.16.0/adventure-api-4.16.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-key/4.16.0/adventure-key-4.16.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/examination-api/1.3.0/examination-api-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/examination-string/1.3.0/examination-string-1.3.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/org/jetbrains/annotations/24.1.0/annotations-24.1.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-bukkit/4.3.2/adventure-platform-bukkit-4.3.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-api/4.3.2/adventure-platform-api-4.3.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-bungeecord/4.3.2/adventure-text-serializer-bungeecord-4.3.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-nbt/4.13.1/adventure-nbt-4.13.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson-legacy-impl/4.13.1/adventure-text-serializer-gson-legacy-impl-4.13.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-facet/4.3.2/adventure-platform-facet-4.3.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-platform-viaversion/4.3.2/adventure-platform-viaversion-4.3.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-minimessage/4.16.0/adventure-text-minimessage-4.16.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-gson/4.16.0/adventure-text-serializer-gson-4.16.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-json/4.16.0/adventure-text-serializer-json-4.16.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/option/1.0.0/option-1.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/com/google/code/gson/gson/2.8.0/gson-2.8.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [ItemEdit] Loaded library /home/<USER>/libraries/net/kyori/adventure-text-serializer-legacy/4.16.0/adventure-text-serializer-legacy-4.16.0.jar
[17:31:23] [Server thread/WARN]: [RoseStacker] Loading Paper plugin in the legacy plugin loading logic. This is not recommended and may introduce some differences into load order. It's highly recommended you move away from this if you are wanting to use Paper plugins.
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [RoseLoot] Loading 1 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [RoseLoot] Loaded library /home/<USER>/libraries/org/xerial/sqlite-jdbc/3.42.0.0/sqlite-jdbc-3.42.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loading 6 libraries... please wait
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/hibernate/orm/hibernate-hikaricp/6.6.13.Final/hibernate-hikaricp-6.6.13.Final.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/jboss/logging/jboss-logging/3.5.0.Final/jboss-logging-3.5.0.Final.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/hibernate/orm/hibernate-core/6.6.13.Final/hibernate-core-6.6.13.Final.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/jakarta/xml/bind/jakarta.xml.bind-api/4.0.0/jakarta.xml.bind-api-4.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/jakarta/activation/jakarta.activation-api/2.1.0/jakarta.activation-api-2.1.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/glassfish/jaxb/jaxb-runtime/4.0.2/jaxb-runtime-4.0.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/glassfish/jaxb/jaxb-core/4.0.2/jaxb-core-4.0.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/eclipse/angus/angus-activation/2.0.0/angus-activation-2.0.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/glassfish/jaxb/txw2/4.0.2/txw2-4.0.2.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/sun/istack/istack-commons-runtime/4.1.1/istack-commons-runtime-4.1.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/h2database/h2/2.3.232/h2-2.3.232.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/zaxxer/HikariCP/6.2.1/HikariCP-6.2.1.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/org/mariadb/jdbc/mariadb-java-client/3.5.3/mariadb-java-client-3.5.3.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar
[17:31:23] [Server thread/INFO]: [SpigotLibraryLoader] [LitMinions] Loaded library /home/<USER>/libraries/com/google/protobuf/protobuf-java/3.21.9/protobuf-java-3.21.9.jar
[17:31:23] [Server thread/INFO]: [FreedomChat] Loading server plugin FreedomChat v1.7.2
[17:31:23] [Server thread/INFO]: [WildTools] Loading server plugin WildTools v2025.1
[17:31:23] [Server thread/INFO]: [ViaVersion] Loading server plugin ViaVersion v5.3.2-SNAPSHOT
[17:31:23] [Server thread/INFO]: [ViaVersion] ViaVersion 5.3.2-SNAPSHOT is now loaded. Registering protocol transformers and injecting...
[17:31:23] [Via-Mappingloader-0/INFO]: [ViaVersion] Loading block connection mappings ...
[17:31:23] [Via-Mappingloader-0/INFO]: [ViaVersion] Using FastUtil Long2ObjectOpenHashMap for block connections
[17:31:23] [Server thread/INFO]: [ViaBackwards] Loading translations...
[17:31:23] [Server thread/INFO]: [ViaBackwards] Registering protocols...
[17:31:24] [Server thread/INFO]: [ViaRewind] Registering protocols...
[17:31:24] [Server thread/INFO]: [NBTAPI] Loading server plugin NBTAPI v2.14.1
[17:31:24] [Server thread/INFO]: [NBTAPI] [NBTAPI] Found Minecraft: 1.21.4! Trying to find NMS support
[17:31:24] [Server thread/INFO]: [NBTAPI] [NBTAPI] NMS support 'MC1_21_R3' loaded!
[17:31:24] [Server thread/INFO]: [NBTAPI] [NBTAPI] Using the plugin 'NBTAPI' to create a bStats instance!
[17:31:24] [Server thread/INFO]: [ViaBackwards] Loading server plugin ViaBackwards v5.3.2-SNAPSHOT
[17:31:24] [Server thread/INFO]: [LuckPerms] Loading server plugin LuckPerms v5.4.158
[17:31:24] [Server thread/INFO]: [GiftCode24] Loading server plugin GiftCode24 v2.1.0-Stable
[17:31:24] [Server thread/INFO]: [Backuper] Loading server plugin Backuper v3.4.1
[17:31:24] [Server thread/INFO]: [CommandAPI] Loaded platform NMS_1_21_R3 > NMS_Common > CommandAPIBukkit
[17:31:24] [Server thread/INFO]: [CommandAPI] Hooked into Spigot successfully for Chat/ChatComponents
[17:31:24] [Server thread/INFO]: [CommandAPI] Hooked into Adventure for AdventureChat/AdventureChatComponents
[17:31:24] [Server thread/INFO]: [CommandAPI] Hooked into Paper for paper-specific API implementations
[17:31:24] [Server thread/INFO]: [LoneLibs] Loading server plugin LoneLibs v1.0.65
[17:31:24] [Server thread/INFO]: [ProtocolLib] Loading server plugin ProtocolLib v5.4.0-SNAPSHOT-742
[17:31:24] [Server thread/WARN]: [ProtocolLib] Version (MC: 1.21.4) has not yet been tested! Proceed with caution.
[17:31:24] [Thread-7/WARN]: [NBTAPI] [NBTAPI] The NBT-API in 'NBTAPI' seems to be outdated!
[17:31:24] [Thread-7/WARN]: [NBTAPI] [NBTAPI] Current Version: '2.14.1' Newest Version: 2.15.1'
[17:31:24] [Thread-7/WARN]: [NBTAPI] [NBTAPI] Please update the NBTAPI or the plugin that contains the api(nag the mod author when the newest release has an old version, not the NBTAPI dev)!
[17:31:24] [Server thread/INFO]: [ConsoleSpamFixReborn] Loading server plugin ConsoleSpamFixReborn v1.11.5
[17:31:24] [Server thread/INFO]: [InventoryRollbackPlus] Loading server plugin InventoryRollbackPlus v1.7.0
[17:31:24] [Server thread/INFO]: [CustomOres] Loading server plugin CustomOres v6.5
[17:31:24] [Server thread/INFO]: [PlaceholderAPI] Loading server plugin PlaceholderAPI v2.11.6
[17:31:24] [Server thread/INFO]: [InsaneAnnouncer] Loading server plugin InsaneAnnouncer v1.4.5
[17:31:24] [Server thread/INFO]: [PlaytimeRewardsPlus] Loading server plugin PlaytimeRewardsPlus v1.1.1
[17:31:24] [Server thread/INFO]: [ReplenishEnchant] Loading server plugin ReplenishEnchant v1.1
[17:31:24] [Server thread/INFO]: [LiteBans] Loading server plugin LiteBans v2.17.2
[17:31:24] [Server thread/INFO]: [AxVaults] Loading server plugin AxVaults v2.9.0
[17:31:24] [Server thread/INFO]: [HamsterAPI] Loading server plugin HamsterAPI v0.2.5
[17:31:24] [Server thread/INFO]: [VouchersPlus] Loading server plugin VouchersPlus v1.1.8
[17:31:24] [Server thread/INFO]: [ExploitFixer] Loading server plugin ExploitFixer v3.2.1
[17:31:24] [Server thread/INFO]: [Vault] Loading server plugin Vault v1.7.3-b131
[17:31:24] [Server thread/INFO]: [floodgate] Loading server plugin floodgate v2.2.4-SNAPSHOT (b116-0e3163c)
[17:31:24] [Server thread/INFO]: [floodgate] Took 340ms to boot Floodgate
[17:31:24] [Server thread/INFO]: [PlayerData] Loading server plugin PlayerData v1.2
[17:31:24] [Server thread/INFO]: [SbmagicTop] Loading server plugin SbmagicTop v1.0.0
[17:31:24] [Server thread/INFO]: [ViaRewind] Loading server plugin ViaRewind v4.0.7
[17:31:24] [Server thread/INFO]: [InvSee++] Loading server plugin InvSeePlusPlus v0.29.23-SNAPSHOT
[17:31:24] [Server thread/INFO]: [PlayerPoints] Loading server plugin PlayerPoints v3.3.0
[17:31:24] [Server thread/INFO]: [PlayerPoints] Initializing using RoseGarden v1.4.6
[17:31:24] [Server thread/INFO]: [DeluxeTags] Loading server plugin DeluxeTags v1.8.2-Release
[17:31:24] [Server thread/INFO]: [packetevents] Loading server plugin packetevents v2.8.0
[17:31:25] [Server thread/INFO]: [MyCommand] Loading server plugin MyCommand v5.7.4
[17:31:25] [Server thread/INFO]: [LPC] Loading server plugin LPC v3.6.1
[17:31:25] [Server thread/INFO]: [SafeNET] Loading server plugin SafeNET v4.0
[17:31:25] [Server thread/INFO]: [SimpPay] Loading server plugin SimpPay v1.2.4-BETA
[17:31:25] [Server thread/INFO]: [LPX] Loading server plugin LPX v3.5.12
[17:31:26] [Server thread/INFO]: [TaiXiu] Loading server plugin TaiXiu v2.6
[17:31:26] [Server thread/INFO]: [DeluxeCoinflip] Loading server plugin DeluxeCoinflip v2.9.6
[17:31:26] [Server thread/INFO]: [FastAsyncWorldEdit] Loading server plugin FastAsyncWorldEdit v2.13.1-SNAPSHOT-1075;fdc9d6d
[17:31:26] [Server thread/WARN]: 
**********************************************
** You are using the Spigot-mapped FAWE jar on a modern Paper version.
** This will result in slower first-run times and wasted disk space from plugin remapping.
** Download the Paper FAWE jar from Modrinth to avoid this: https://modrinth.com/plugin/fastasyncworldedit/
**********************************************
[17:31:27] [Server thread/INFO]: Got request to register class com.sk89q.worldedit.bukkit.BukkitServerInterface with WorldEdit [com.sk89q.worldedit.extension.platform.PlatformManager@59a0bd29]
[17:31:27] [Server thread/INFO]: [LiteSignIn] Loading server plugin LiteSignIn v*******
[17:31:27] [Server thread/INFO]: [InvSee++_Clear] Loading server plugin InvSeePlusPlus_Clear v0.29.23-SNAPSHOT
[17:31:27] [Server thread/INFO]: [WorldGuard] Loading server plugin WorldGuard v7.0.13+82fdc65
[17:31:27] [Server thread/INFO]: [ClansPlus] Loading server plugin ClansPlus v1.7
[17:31:27] [Server thread/INFO]: [SkinsRestorer] Loading server plugin SkinsRestorer v15.7.7
[17:31:27] [Server thread/INFO]: [UltraBar] Loading server plugin UltraBar v*******
[17:31:27] [Server thread/INFO]: [BeaconPlus] Loading server plugin BeaconPlus3 v3.1.0
[17:31:27] [Server thread/INFO]: [BeaconPlus] Plugin API version has been changed to 1.13
[17:31:27] [Server thread/INFO]: [WorldGuardExtraFlags] Loading server plugin WorldGuardExtraFlags v4.2.4-SNAPSHOT
[17:31:27] [Server thread/INFO]: [Essentials] Loading server plugin Essentials v2.21.0
[17:31:27] [Server thread/INFO]: [HeadDatabase] Loading server plugin HeadDatabase v4.21.2
[17:31:27] [Server thread/INFO]: [nightcore] Loading server plugin nightcore v*******
[17:31:27] [Server thread/INFO]: [FancyHolograms] Loading server plugin FancyHolograms v2.4.2
[17:31:27] [Server thread/INFO]: [Orestack] Loading server plugin Orestack v3.28.1
[17:31:27] [Server thread/INFO]: [LibsDisguises] Loading server plugin LibsDisguises v11.0.5
[17:31:27] [FancyLogger/INFO]: [FancyHolograms] (Server thread) INFO: Successfully loaded FancyHolograms version 2.4.2
[17:31:27] [Server thread/INFO]: [ExcellentCrates] Loading server plugin ExcellentCrates v6.2.2
[17:31:27] [Server thread/INFO]: [PlayerAuctions] Loading server plugin PlayerAuctions v1.31.1
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Loading server plugin ajLeaderboards v2.8.0
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for gson
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for gson
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for jar-relocator
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for jar-relocator
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for asm
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for asm
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for asm-commons
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for asm-commons
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for gson
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for gson
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for HikariCP
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for HikariCP
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for slf4j-api
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for slf4j-api
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Verifying checksum for h2
[17:31:27] [Server thread/INFO]: [ajLeaderboards] Checksum matched for h2
[17:31:27] [Server thread/INFO]: [SuperVanish] Loading server plugin SuperVanish v6.2.20
[17:31:27] [Server thread/INFO]: [InvSee++_Give] Loading server plugin InvSeePlusPlus_Give v0.29.23-SNAPSHOT
[17:31:27] [Server thread/INFO]: [antiRedstoneClock] Loading server plugin antiRedstoneClock v1.5.0
[17:31:27] [Server thread/WARN]: [antiRedstoneClock] WorldGuard 7 is not supported
[17:31:27] [Server thread/INFO]: [CoinsEngine] Loading server plugin CoinsEngine v2.4.2
[17:31:27] [Server thread/INFO]: [GSit] Loading server plugin GSit v2.3.2
[17:31:28] [Server thread/INFO]: [AxTrade] Loading server plugin AxTrade v1.17.0
[17:31:28] [Server thread/INFO]: [Skript] Loading server plugin Skript v2.11.2
[17:31:28] [Server thread/INFO]: [Citizens] Loading server plugin Citizens v2.0.37-SNAPSHOT (build 3760)
[17:31:28] [Server thread/INFO]: [TAB] Loading server plugin TAB v5.2.0
[17:31:28] [Server thread/INFO]: [EssentialsSpawn] Loading server plugin EssentialsSpawn v2.21.0
[17:31:28] [Server thread/INFO]: [DecentHolograms] Loading server plugin DecentHolograms v2.8.17
[17:31:28] [Server thread/INFO]: [skript-placeholders] Loading server plugin skript-placeholders v1.7.0
[17:31:28] [Server thread/INFO]: [HeadBlocks] Loading server plugin HeadBlocks v2.6.15
[17:31:28] [Server thread/INFO]: [BetterFarming] Loading server plugin BetterFarming v5.10.4
[17:31:28] [Server thread/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[17:31:28] [Server thread/INFO]: [SuperiorSkyblock2] Loading server plugin SuperiorSkyblock2 v2024.4-b445
[17:31:28] [Server thread/INFO]: [Multiverse-Core] Loading server plugin Multiverse-Core v4.3.16
[17:31:28] [Server thread/INFO]: [CoreTools] Loading server plugin CoreTools v1.2-SNAPSHOT
[17:31:28] [Server thread/INFO]: [Shopkeepers] Loading server plugin Shopkeepers v2.23.8
[17:31:28] [Server thread/INFO]: [Shopkeepers] Loaded all plugin classes (379 ms).
[17:31:28] [Server thread/INFO]: [Shopkeepers] Compatibility provider loaded: 1_21_R4
[17:31:28] [Server thread/INFO]: [Shopkeepers] Loading config.
[17:31:28] [Server thread/INFO]: [Shopkeepers] Loading language file: language-en-default.yml
[17:31:28] [Server thread/INFO]: [Shopkeepers] Registering WorldGuard flag 'allow-shop'.
[17:31:28] [Server thread/INFO]: [Shopkeepers] Registering defaults.
[17:31:28] [Server thread/INFO]: [UltimateKoth] Loading server plugin UltimateKoth v2.13.0
[17:31:28] [Server thread/INFO]: [AdvancedOreGen] Loading server plugin AdvancedOreGen v1.6.69-SNAPSHOT
[17:31:28] [Server thread/INFO]: [DiscordSRV] Loading server plugin DiscordSRV v1.29.0
[17:31:28] [Server thread/INFO]: [SCore] Loading server plugin SCore v*********
[17:31:28] [Server thread/INFO]: [MythicMobs] Loading server plugin MythicMobs v5.9.2-SNAPSHOT-538a490c
[17:31:28] [Server thread/INFO]: [LumineUtils] (io.lumine.mythic.bukkit.utils.) is bound to plugin MythicMobs - io.lumine.mythic.bukkit.MythicBukkit
[17:31:28] [Server thread/INFO]: [MythicMobs] §x§f§f§0§0§0§0M§x§f§f§6§6§0§0y§x§f§f§c§c§0§0t§x§c§b§f§f§0§0h§x§6§5§f§f§0§0i§x§0§0§f§f§0§0c§x§0§0§f§f§6§6 §x§0§0§f§f§c§bE§x§0§0§c§b§f§fn§x§0§0§6§5§f§fa§x§0§0§0§0§f§fb§x§6§6§0§0§f§fl§x§c§c§0§0§f§fe§x§f§f§0§0§c§cd§x§f§f§0§0§6§6!
[17:31:28] [Server thread/INFO]: [Quests] Loading server plugin Quests v5.2.2-b533
[17:31:28] [Server thread/INFO]: [BattlePass] Loading server plugin BattlePass v4.9.15
[17:31:28] [Server thread/INFO]: [EpicCraftingsPlus] Loading server plugin EpicCraftingsPlus v7.33.1
[17:31:28] [Server thread/INFO]: [MythicAnnouncer] Loading server plugin MythicAnnouncer v1.5.0
[17:31:28] [Server thread/INFO]: [PhoBan] Loading server plugin PhoBan v1.0.0
[17:31:28] [Server thread/INFO]: [MythicDungeons] Loading server plugin MythicDungeons v2.0.1-SNAPSHOT
[17:31:28] [Server thread/INFO]: [ExecutableEvents] Loading server plugin ExecutableEvents v*********
[17:31:28] [Server thread/INFO]: [AuraSkills] Loading server plugin AuraSkills v2.3.3
[17:31:28] [Server thread/INFO]: [PhoBanPro] Loading server plugin PhoBanPro v1.2.11
[17:31:28] [Server thread/INFO]: [InteractiveChat] Loading server plugin InteractiveChat v*******
[17:31:28] [Server thread/INFO]: [MythicCrucible] Loading server plugin MythicCrucible v2.2.0-SNAPSHOT
[17:31:28] [Server thread/INFO]: [ModelEngine] Loading server plugin ModelEngine vR4.0.9
[17:31:28] [Server thread/INFO]: [MMDiscordNotifs] Loading server plugin MMDiscordNotifs v1.1
[17:31:28] [Server thread/INFO]: [MythicLib] Loading server plugin MythicLib v1.7.1-SNAPSHOT
[17:31:28] [Server thread/INFO]: [MythicLib] Plugin file is called 'MythicLib-dist-1.7.1-20250707.185019-28.jar'
[17:31:28] [Server thread/INFO]: [MythicLib] Detected Bukkit Version: v1_21_R3
[17:31:28] [Server thread/INFO]: [MythicLib] Hooked onto WorldGuard
[17:31:28] [Server thread/INFO]: [Vulcan] Loading server plugin Vulcan v2.9.5
[17:31:28] [Server thread/INFO]: [MMOItems] Loading server plugin MMOItems v6.10.1-SNAPSHOT
[17:31:28] [Server thread/INFO]: [MMOItems] Plugin file is called 'MMOItems-6.10.1-20250707.184359-27.jar'
[17:31:29] [Server thread/INFO]: [MMOItems] Hooked onto WorldEdit
[17:31:29] [Server thread/INFO]: [MMOItems Template Modifiers] Preloading template modifiers, please wait..
[17:31:29] [Server thread/INFO]: [MMOItems Item Templates] Preloading item templates, please wait..
[17:31:29] [Server thread/INFO]: [MMOItems] Hooked onto MythicMobs
[17:31:29] [Server thread/INFO]: [PandeLoot] Loading server plugin PandeLoot vdev
[17:31:29] [Server thread/INFO]: [MMOInventory] Loading server plugin MMOInventory v2.0-SNAPSHOT
[17:31:29] [Server thread/INFO]: [MMOInventory] Plugin file is called 'MMOInventory-2.0-20250623.202533-5.jar'
[17:31:29] [Server thread/INFO]: [BeeMinions] Loading server plugin BeeMinions v5.0.2-BETA
[17:31:29] [Server thread/INFO]: [ItemsAdder] Loading server plugin ItemsAdder v4.0.11
[17:31:29] [Server thread/INFO]: [DeluxeBazaar] Loading server plugin DeluxeBazaar v9.9
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading server plugin AxEnvoy v2.0.6
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library commons-math3
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library caffeine
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library slf4j-api
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library commons-io
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library commons-text
[17:31:29] [Server thread/INFO]: [AxEnvoy] Loading library commons-math3
[17:31:29] [Server thread/INFO]: [DeluxeMenus] Loading server plugin DeluxeMenus v1.14.0-Release
[17:31:29] [Server thread/WARN]: [DeluxeMenus] Could not setup a NMS hook for your server version!
[17:31:29] [Server thread/INFO]: [SoulSkills] Loading server plugin SoulSkills v1.0
[17:31:29] [Server thread/INFO]: [zMenu] Loading server plugin zMenu v*******
[17:31:29] [Server thread/INFO]: [CustomCrops] Loading server plugin CustomCrops v3.6.40
[17:31:29] [Server thread/INFO]: [InteractiveChatDiscordSRVAddon] Loading server plugin InteractiveChatDiscordSrvAddon v*******
[17:31:29] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechatdiscordsrvaddon.listeners.DiscordCommandEvents subscribed (1 methods)
[17:31:29] [Server thread/INFO]: [ShopGUIPlus] Loading server plugin ShopGUIPlus v1.106.1
[17:31:29] [Server thread/INFO]: [AdvancedChests] Loading server plugin AdvancedChests v43.5
[17:31:29] [Server thread/INFO]: [LitLibs] Loading server plugin LitLibs v1.1.32
[17:31:29] [Server thread/INFO]: [FeatheredElection] Loading server plugin FeatheredElection v5.0.4-BETA
[17:31:29] [Server thread/INFO]: [ItemEdit] Loading server plugin ItemEdit v3.7.0
[17:31:29] [Server thread/INFO]: [RoseStacker] Loading server plugin RoseStacker v1.5.33
[17:31:29] [Server thread/INFO]: [RoseLoot] Loading server plugin RoseLoot v1.3.0
[17:31:29] [Server thread/INFO]: [RoseLoot] Initializing using RoseGarden v1.4.8-SNAPSHOT
[17:31:29] [Server thread/INFO]: [ExtraStorage] Loading server plugin ExtraStorage v1.0
[17:31:29] [Server thread/INFO]: [LitMinions] Loading server plugin LitMinions v4.4.6
[17:31:29] [Server thread/INFO]: [PlugManX] Loading server plugin PlugManX v2.4.1
[17:31:29] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[17:31:29] [Server thread/INFO]: [LuckPerms] Enabling LuckPerms v5.4.158
[17:31:30] [Server thread/INFO]:         __    
[17:31:30] [Server thread/INFO]:   |    |__)   LuckPerms v5.4.158
[17:31:30] [Server thread/INFO]:   |___ |      Running on Bukkit - Leaf
[17:31:30] [Server thread/INFO]: 
[17:31:30] [Server thread/INFO]: [LuckPerms] Loading configuration...
[17:31:30] [Server thread/INFO]: [LuckPerms] Loading storage provider... [H2]
[17:31:31] [Server thread/INFO]: [LuckPerms] Loading internal permission managers...
[17:31:31] [Server thread/INFO]: [LuckPerms] Performing initial data load...
[17:31:31] [Server thread/INFO]: [LuckPerms] Successfully enabled. (took 1961ms)
[17:31:31] [Server thread/INFO]: [GiftCode24] Enabling GiftCode24 v2.1.0-Stable
[17:31:31] [Server thread/INFO]: [GiftCode24]  
[17:31:31] [Server thread/INFO]: [GiftCode24] §a ██████╗ ██╗███████╗████████╗ ██████╗ ██████╗ ██████╗ ███████╗██████╗ ██╗  ██╗
[17:31:31] [Server thread/INFO]: [GiftCode24] §a██╔════╝ ██║██╔════╝╚══██╔══╝██╔════╝██╔═══██╗██╔══██╗██╔════╝╚════██╗██║  ██║
[17:31:31] [Server thread/INFO]: [GiftCode24] §a██║  ███╗██║█████╗     ██║   ██║     ██║   ██║██║  ██║█████╗   █████╔╝███████║
[17:31:31] [Server thread/INFO]: [GiftCode24] §a██║   ██║██║██╔══╝     ██║   ██║     ██║   ██║██║  ██║██╔══╝  ██╔═══╝ ╚════██║
[17:31:31] [Server thread/INFO]: [GiftCode24] §a╚██████╔╝██║██║        ██║   ╚██████╗╚██████╔╝██████╔╝███████╗███████╗     ██║
[17:31:31] [Server thread/INFO]: [GiftCode24] §a ╚═════╝ ╚═╝╚═╝        ╚═╝    ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝╚══════╝     ╚═╝
[17:31:31] [Server thread/INFO]: [GiftCode24]  
[17:31:31] [Server thread/INFO]: [GiftCode24] §6  Tác giả: QuangDev05
[17:31:31] [Server thread/INFO]: [GiftCode24] §e  Phiên bản: v2.1.0-Stable
[17:31:31] [Server thread/INFO]: [GiftCode24]  
[17:31:31] [Server thread/INFO]: [LoneLibs] Enabling LoneLibs v1.0.65
[17:31:31] [Server thread/INFO]: [ProtocolLib] Enabling ProtocolLib v5.4.0-SNAPSHOT-742
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Enabling ConsoleSpamFixReborn v1.11.5
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Initializing ConsoleSpamFixReborn
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Server version detected: 1.21.4
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Loading the config file...
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Config file loaded!
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Attempting to register command executor for 'csf'
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Command 'csf' found. Setting executor...
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] Command executor for 'csf' set successfully.
[17:31:31] [Server thread/INFO]: [ConsoleSpamFixReborn] ConsoleSpamFixReborn loaded successfully!
[17:31:31] [Server thread/INFO]: [InsaneAnnouncer] Enabling InsaneAnnouncer v1.4.5
[17:31:31] [Server thread/INFO]: [InsaneAnnouncer] Loading Plugin...
[17:31:32] [Server thread/INFO]: [InsaneAnnouncer] Successfully loaded in 340 ms
[17:31:32] [Server thread/INFO]: [Vault] Enabling Vault v1.7.3-b131
[17:31:32] [Server thread/INFO]: [Vault] [Economy] Essentials Economy found: Waiting
[17:31:32] [Server thread/INFO]: [Vault] [Permission] SuperPermissions loaded as backup permission system.
[17:31:32] [Server thread/INFO]: [Vault] Enabled Version 1.7.3-b131
[17:31:32] [Server thread/INFO]: [LuckPerms] Registered Vault permission & chat hook.
[17:31:32] [Server thread/INFO]: [ViaRewind] Enabling ViaRewind v4.0.7
[17:31:32] [Server thread/INFO]: [PlayerPoints] Enabling PlayerPoints v3.3.0
[17:31:32] [Server thread/INFO]: [PlayerPoints] Data handler connected using SQLite.
[17:31:32] [Server thread/INFO]: [SafeNET] Enabling SafeNET v4.0
[17:31:32] [Server thread/INFO]: [SafeNET] Paper server components available; handshakes will be handled via the API and sessions will not be validated.
[17:31:32] [Server thread/INFO]: [FastAsyncWorldEdit] Enabling FastAsyncWorldEdit v2.13.1-SNAPSHOT-1075;fdc9d6d
[17:31:32] [Server thread/INFO]: [com.fastasyncworldedit.core.Fawe] LZ4 Compression Binding loaded successfully
[17:31:32] [Server thread/INFO]: [com.fastasyncworldedit.core.Fawe] ZSTD Compression Binding loaded successfully
[17:31:32] [Server thread/INFO]: Registering commands with com.sk89q.worldedit.bukkit.BukkitServerInterface
[17:31:32] [Server thread/INFO]: WEPIF: Vault detected! Using Vault for permissions
[17:31:32] [Server thread/INFO]: Using com.sk89q.worldedit.bukkit.adapter.impl.fawe.v1_21_4.PaperweightFaweAdapter as the Bukkit adapter
[17:31:33] [Server thread/INFO]: [SkinsRestorer] Enabling SkinsRestorer v15.7.7
[17:31:33] [Server thread/INFO]: [SkinsRestorer] -------------------------/Warning\-------------------------
[17:31:33] [Server thread/INFO]: [SkinsRestorer] This plugin is running in PROXY mode!
[17:31:33] [Server thread/INFO]: [SkinsRestorer] You have to put the same config.yml on all servers and on the proxy.
[17:31:33] [Server thread/INFO]: [SkinsRestorer] (<proxy>/plugins/SkinsRestorer/)
[17:31:33] [Server thread/INFO]: [SkinsRestorer] -------------------------\Warning/-------------------------
[17:31:33] [Server thread/INFO]: [SkinsRestorer] Running on Minecraft 1.21.4.
[17:31:33] [Server thread/WARN]: [SkinsRestorer] Proxy mode API is enabled (server.proxyMode.api), but MySQL is not set up, this is not supported. You must configure MySQL on all servers and on the proxy and use the same database.
[17:31:33] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: skinsrestorer [15.7.7]
[17:31:33] [Server thread/INFO]: [SkinsRestorer] PlaceholderAPI expansion registered!
[17:31:33] [Server thread/INFO]: [nightcore] Enabling nightcore v*******
[17:31:33] [Server thread/INFO]: [nightcore] Server version detected as 1.21.4. Using paper-bridge.
[17:31:33] [Server thread/INFO]: [nightcore] Found permissions provider: LuckPerms
[17:31:33] [Server thread/INFO]: [nightcore] Found economy provider: EssentialsX Economy
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer] ----------------------------------------------
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     +==================+
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     |   SkinsRestorer  |
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     |------------------|
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     |    Proxy Mode    |
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     +==================+
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer] ----------------------------------------------
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     Version: 15.7.7
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     Commit: 7eff058
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer]     This is the latest version!
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer] ----------------------------------------------
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer] Do you have issues? Read our troubleshooting guide: https://skinsrestorer.net/docs/troubleshooting
[17:31:33] [Folia Async Scheduler Thread #0/INFO]: [SkinsRestorer] Want to support SkinsRestorer? Consider donating: https://skinsrestorer.net/donate
[17:31:34] [Server thread/INFO]: [nightcore] Time zone set as Asia/Saigon
[17:31:34] [Server thread/INFO]: [nightcore] Plugin loaded in 62 ms!
[17:31:34] [Server thread/INFO]: [CoinsEngine] Enabling CoinsEngine v2.4.2
[17:31:34] [Server thread/INFO]: [CoinsEngine] Powered by nightcore
[17:31:34] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-1 - Starting...
[17:31:34] [Server thread/INFO]: [com.zaxxer.hikari.pool.HikariPool] HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@313ec45d
[17:31:34] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-1 - Start completed.
[17:31:34] [ForkJoinPool.commonPool-worker-2/WARN]: [com.fastasyncworldedit.core.util.UpdateNotification] An update for FastAsyncWorldEdit is available. You are 55 build(s) out of date.
You are running build 1075, the latest version is build 1130.
Update at https://ci.athion.net/job/FastAsyncWorldEdit
[17:31:34] [Server thread/INFO]: [CoinsEngine] Registered: 'crystal' currency!
[17:31:34] [Server thread/INFO]: [CoinsEngine] Detected plugin available for data migration: PlayerPoints
[17:31:34] [Server thread/INFO]: [CoinsEngine] Detected plugin available for data migration: Vault
[17:31:34] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: coinsengine [2.4.2]
[17:31:34] [Server thread/INFO]: [CoinsEngine] Plugin loaded in 129 ms!
[17:31:34] [Server thread/INFO]: [ModelEngine] Enabling ModelEngine vR4.0.9
[17:31:34] [Server thread/INFO]: [ModelEngine] [S] Loading cache version: R4.0.8
[17:31:34] [Server thread/INFO]: [PlugManX] Enabling PlugManX v2.4.1
[17:31:34] [Server thread/WARN]: [PlugManX] ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[17:31:34] [Server thread/WARN]: [PlugManX] It seems like you're running on paper.
[17:31:34] [Server thread/WARN]: [PlugManX] PlugManX cannot interact with paper-plugins, yet.
[17:31:34] [Server thread/WARN]: [PlugManX] Also, if you encounter any issues, please join my discord: https://discord.gg/GxEFhVY6ff
[17:31:34] [Server thread/WARN]: [PlugManX] Or create an issue on GitHub: https://github.com/TheBlackEntity/PlugMan
[17:31:34] [Server thread/WARN]: [PlugManX] ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
[17:31:34] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[17:31:34] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[17:31:34] [Server thread/WARN]: Whilst this makes it possible to use BungeeCord, unless access to your server is properly restricted, it also opens up the ability for hackers to connect with any username they choose.
[17:31:34] [Server thread/WARN]: Please see http://www.spigotmc.org/wiki/firewall-guide/ for further information.
[17:31:34] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[17:31:34] [Server thread/INFO]: Preparing level "world"
[17:31:34] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[17:31:34] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:35] [Server thread/INFO]: Time elapsed: 177 ms
[17:31:35] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[17:31:35] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:35] [Server thread/INFO]: Time elapsed: 247 ms
[17:31:35] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[17:31:35] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:35] [Server thread/INFO]: Time elapsed: 30 ms
[17:31:35] [Server thread/INFO]: [FreedomChat] Enabling FreedomChat v1.7.2
[17:31:35] [Server thread/INFO]: [WildTools] Enabling WildTools v2025.1
[17:31:35] [Server thread/INFO]: [WildTools] ******** ENABLE START ********
[17:31:35] [Server thread/INFO]: [WildTools] Loading configuration started...
[17:31:35] [Server thread/INFO]: [WildTools]  - Found 12 tools in config.yml.
[17:31:35] [Server thread/INFO]: [WildTools] Loading configuration done (Took 21ms)
[17:31:35] [Server thread/INFO]: [WildTools] Loading messages started...
[17:31:35] [Server thread/INFO]: [WildTools]  - Found 0 messages in lang.yml.
[17:31:35] [Server thread/INFO]: [WildTools] Loading messages done (Took 2ms)
[17:31:35] [Server thread/INFO]: [WildTools] ******** ENABLE DONE ********
[17:31:35] [Server thread/INFO]: [ViaVersion] Enabling ViaVersion v5.3.2-SNAPSHOT
[17:31:35] [Server thread/INFO]: [ViaVersion] ViaVersion detected server version: 1.21.4 (769)
[17:31:35] [Server thread/INFO]: [ModelEngine] [S] Compatibility applied: ViaVersion
[17:31:35] [Server thread/INFO]: [ViaBackwards] Enabling ViaBackwards v5.3.2-SNAPSHOT
[17:31:35] [Server thread/INFO]: [Backuper] Enabling Backuper v3.4.1
[17:31:35] [Server thread/INFO]: [CommandAPI] Hooked into Paper ServerResourcesReloadedEvent
[17:31:35] [Server thread/INFO]: [Backuper] Loading config...
[17:31:35] [Server thread/INFO]: [Backuper] Config loading completed
[17:31:35] [Server thread/INFO]: [Backuper] Indexing storages...
[17:31:35] [Server thread/INFO]: [Backuper] Unifying backup names format
[17:31:35] [Server thread/INFO]: [Backuper] Backup names format unification completed
[17:31:35] [Server thread/INFO]: [Backuper] Initializing BStats...
[17:31:35] [Server thread/INFO]: [Backuper] BStats initialization completed
[17:31:36] [Server thread/INFO]: [Backuper] 
---------------------------------------------------------------------------
Please, if you find any issues related to the Backuper
Create an issue using the link: https://github.com/DVDishka/Backuper/issues
---------------------------------------------------------------------------
[17:31:36] [Server thread/INFO]: 
---------------------------------------------------------------------------
You are using an outdated version of Backuper!
Please update it to the latest and check the changelist!
Download link: https://modrinth.com/plugin/backuper/versions#all-versions
Download link: https://hangar.papermc.io/Collagen/Backuper
---------------------------------------------------------------------------
[17:31:36] [Server thread/INFO]: [Backuper] Backuper plugin has been enabled!
[17:31:36] [Server thread/INFO]: [InventoryRollbackPlus] Enabling InventoryRollbackPlus v1.7.0
[17:31:36] [Server thread/INFO]: [InventoryRollbackPlus] Attempting support for version: 1.21.4-496-5311ae8 (MC: 1.21.4)
[17:31:36] [Server thread/INFO]: [InventoryRollbackPlus] Using CraftBukkit version: v1_21_R3
[17:31:36] [Server thread/INFO]: [InventoryRollbackPlus] Inventory backup data is set to save to: YAML
[17:31:36] [Server thread/INFO]: [InventoryRollbackPlus] All tests completed. (5 tests)
[17:31:36] [Server thread/INFO]: [CustomOres] Enabling CustomOres v6.5
[17:31:36] [Server thread/INFO]: Custom Ores Loaded Successfully
[17:31:36] [Server thread/INFO]: [PlaceholderAPI] Enabling PlaceholderAPI v2.11.6
[17:31:36] [Server thread/INFO]: [PlaceholderAPI] Fetching available expansion information...
[17:31:36] [Server thread/INFO]: [PlaytimeRewardsPlus] Enabling PlaytimeRewardsPlus v1.1.1
[17:31:36] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: playtime [1.0.0]
[17:31:36] [Server thread/INFO]: [PlaytimeRewards+] Detected Placeholder API. Successfully enabled plugin support.
[17:31:36] [Server thread/INFO]: [PlaytimeRewards+] Cleaning up...
[17:31:36] [Server thread/INFO]: [PlaytimeRewards+] Accumulating player data for LeaderBoard placeholders, this task will run asynchronously.
[17:31:36] [Server thread/INFO]: [PlaytimeRewards+] Detected version group as V1.20.5+[1214]. Applying necessary settings.
[17:31:36] [Server thread/INFO]: [PlaytimeRewards+] Has started successfully!
[17:31:36] [Server thread/INFO]: [ReplenishEnchant] Enabling ReplenishEnchant v1.1
[17:31:36] [Server thread/INFO]: [ReplenishEnchant] [ReplenishEnchant] plugin has been enabled!
[17:31:36] [Server thread/INFO]: [LiteBans] Enabling LiteBans v2.17.2
[17:31:37] [Server thread/INFO]: [LiteBans] Using system locale (en)
[17:31:37] [Server thread/INFO]: [LiteBans] Loaded 2 templates from templates.yml!
[17:31:37] [Server thread/INFO]: [LiteBans] Loading: h2 1.4.197
[17:31:37] [Server thread/INFO]: [LiteBans] Connecting to database...
[17:31:37] [Server thread/INFO]: [LiteBans] Connected to H2 database successfully (68 ms).
[17:31:37] [Server thread/INFO]: [LiteBans] Database connection fully initialized (70.6 ms).
[17:31:37] [Server thread/INFO]: [LiteBans] v2.17.2 enabled. Startup took 72 ms.
[17:31:37] [Server thread/INFO]: [AxVaults] Enabling AxVaults v2.9.0
[17:31:37] [Server thread/INFO]: [AxVaults] Hooked into PlaceholderAPI!
[17:31:37] [Server thread/INFO]: [AxVaults] Loaded plugin!
[17:31:37] [Server thread/INFO]: [HamsterAPI] Enabling HamsterAPI v0.2.5
[17:31:37] [Server thread/INFO]: [VouchersPlus] Enabling VouchersPlus v1.1.8
[17:31:37] [Server thread/INFO]: [Vouchers+] Starting Plugin...
[17:31:38] [Server thread/ERROR]: Error occurred while enabling VouchersPlus v1.1.8 (Is it up to date?)
java.lang.NullPointerException: Cannot read the array length because "<local2>" is null
	at VouchersPlus-1.1.8.jar/uhfinn.vouchersplus.Modules.YML.ScrapeData.loadAllVoucherIDs(ScrapeData.java:26) ~[VouchersPlus-1.1.8.jar:?]
	at VouchersPlus-1.1.8.jar/uhfinn.vouchersplus.Modules.YML.CustomYMLFiles.Reload(CustomYMLFiles.java:75) ~[VouchersPlus-1.1.8.jar:?]
	at VouchersPlus-1.1.8.jar/uhfinn.vouchersplus.Main.onEnable(Main.java:35) ~[VouchersPlus-1.1.8.jar:?]
	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
[17:31:38] [Server thread/INFO]: [VouchersPlus] Disabling VouchersPlus v1.1.8
[17:31:38] [Server thread/INFO]: [Vouchers+] Successfully Shutdown Plugin...
[17:31:38] [Server thread/INFO]: [ExploitFixer] Enabling ExploitFixer v3.2.1
[17:31:38] [Server thread/INFO]: [ExploitFixer] Successfully hooked with HamsterAPI!
[17:31:38] [Server thread/INFO]: [ExploitFixer] Loaded languages: de, ru, pt, fr, hu, uk, sk, id, sv, ko, en, it, es, zh, cs, ar, vi, th, ja, pl, ro, he, da, tr, nl
[17:31:38] [Server thread/INFO]: [ExploitFixer] Invalid Recipe ID module initialized successfully
[17:31:38] [Server thread/INFO]: [ExploitFixer] Successfully registered commands!
[17:31:38] [Server thread/INFO]: [ExploitFixer] Successfully registered tasks!
[17:31:38] [Server thread/INFO]: [ExploitFixer] Successfully registered listeners!
[17:31:38] [Server thread/INFO]: [floodgate] Enabling floodgate v2.2.4-SNAPSHOT (b116-0e3163c)
[17:31:38] [Server thread/INFO]: [PlayerData] Enabling PlayerData v1.2*
[17:31:38] [Server thread/INFO]: [SbmagicTop] Enabling SbmagicTop v1.0.0
[17:31:38] [Server thread/INFO]: [SbmagicTop] Đã tải config top: balance
[17:31:38] [Server thread/ERROR]: Error occurred while enabling SbmagicTop v1.0.0 (Is it up to date?)
java.lang.NoClassDefFoundError: net/dv8tion/jda/api/JDABuilder
	at sbmagictop-1.0-SNAPSHOT.jar/shyrcs.discordbot.top.SbmagicTopPlugin.initializeDiscordBot(SbmagicTopPlugin.java:68) ~[sbmagictop-1.0-SNAPSHOT.jar:?]
	at sbmagictop-1.0-SNAPSHOT.jar/shyrcs.discordbot.top.SbmagicTopPlugin.onEnable(SbmagicTopPlugin.java:39) ~[sbmagictop-1.0-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
Caused by: java.lang.ClassNotFoundException: net.dv8tion.jda.api.JDABuilder
	at org.bukkit.plugin.java.PluginClassLoader.loadClass0(PluginClassLoader.java:197) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.plugin.java.PluginClassLoader.loadClass(PluginClassLoader.java:164) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	... 14 more
[17:31:38] [Server thread/INFO]: [SbmagicTop] Disabling SbmagicTop v1.0.0
[17:31:38] [Server thread/INFO]: [SbmagicTop] SbmagicTop đã được tắt!
[17:31:38] [Server thread/INFO]: [InvSee++] Enabling InvSeePlusPlus v0.29.23-SNAPSHOT
[17:31:38] [Server thread/INFO]: [DeluxeTags] Enabling DeluxeTags v1.8.2-Release
[17:31:38] [Server thread/INFO]: [DeluxeTags] Using standard hex colors format: #aaFF00
[17:31:38] [Server thread/INFO]: [DeluxeTags] 119 tags loaded
[17:31:38] [Server thread/INFO]: [DeluxeTags] Loading DeluxeTags messages.yml
[17:31:38] [Server thread/INFO]: [DeluxeTags] PAPI Chat enabled. This means your chat plugin will use placeholders to fetch the tags!
[17:31:38] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: deluxetags [1.8.2-Release]
[17:31:38] [Server thread/INFO]: [DeluxeTags] ----------------------------
[17:31:38] [Server thread/INFO]: [DeluxeTags]      DeluxeTags Updater
[17:31:38] [Server thread/INFO]: [DeluxeTags]  
[17:31:38] [Server thread/INFO]: [DeluxeTags] You are running 1.8.2-Release
[17:31:38] [Server thread/INFO]: [DeluxeTags] The latest version
[17:31:38] [Server thread/INFO]: [DeluxeTags] of DeluxeTags!
[17:31:38] [Server thread/INFO]: [DeluxeTags]  
[17:31:38] [Server thread/INFO]: [DeluxeTags] ----------------------------
[17:31:38] [Server thread/INFO]: [packetevents] Enabling packetevents v2.8.0
[17:31:38] [packetevents-update-check-thread/INFO]: [packetevents] Checking for updates, please wait...
[17:31:38] [Server thread/INFO]: [MyCommand] Enabling MyCommand v5.7.4
[17:31:38] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-* MyCommand v.5.7.4*-=-=-=-=-=-=-=-=-=-*
[17:31:38] [Server thread/INFO]: | Hooked on Vault 1.7.3-b131
[17:31:38] [Server thread/INFO]: | Command file(s) found : 3
[17:31:38] [Server thread/INFO]: | Config : Ready.
[17:31:38] [Server thread/INFO]: | ProtocolLib found, features availables (SignMenu)
[17:31:38] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mycommand [1.0.0]
[17:31:38] [Server thread/INFO]: | Placeholder_API : Hooked, Ok.
[17:31:38] [Server thread/INFO]: | Custom commands loaded : 45
[17:31:38] [packetevents-update-check-thread/INFO]: [packetevents] There is an update available for PacketEvents! Your build: (2.8.0) | Latest release: (2.9.3)
[17:31:43] [Thread-18/ERROR]: [MyCommand] The updater could not contact dev.bukkit.org for updating.
[17:31:43] [Thread-18/ERROR]: [MyCommand] If you have not recently modified your configuration and this is the first time you are seeing this message, the site may be experiencing temporary downtime.
[17:31:43] [Thread-18/ERROR]: [MyCommand] null
java.net.SocketTimeoutException: Connect timed out
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:546) ~[?:?]
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592) ~[?:?]
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327) ~[?:?]
	at java.base/java.net.Socket.connect(Socket.java:752) ~[?:?]
	at java.base/sun.security.ssl.SSLSocketImpl.connect(SSLSocketImpl.java:304) ~[?:?]
	at java.base/sun.net.NetworkClient.doConnect(NetworkClient.java:178) ~[?:?]
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:531) ~[?:?]
	at java.base/sun.net.www.http.HttpClient.openServer(HttpClient.java:636) ~[?:?]
	at java.base/sun.net.www.protocol.https.HttpsClient.<init>(HttpsClient.java:264) ~[?:?]
	at java.base/sun.net.www.protocol.https.HttpsClient.New(HttpsClient.java:377) ~[?:?]
	at java.base/sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.getNewHttpClient(AbstractDelegateHttpsURLConnection.java:193) ~[?:?]
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1243) ~[?:?]
	at java.base/sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1129) ~[?:?]
	at java.base/sun.net.www.protocol.https.AbstractDelegateHttpsURLConnection.connect(AbstractDelegateHttpsURLConnection.java:179) ~[?:?]
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1691) ~[?:?]
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1615) ~[?:?]
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getInputStream(HttpsURLConnectionImpl.java:223) ~[?:?]
	at MyCommand-v5.7.4.jar/it.emmerrei.updater.Updater.read(Updater.java:644) ~[MyCommand-v5.7.4.jar:?]
	at MyCommand-v5.7.4.jar/it.emmerrei.updater.Updater.runUpdater(Updater.java:719) ~[MyCommand-v5.7.4.jar:?]
	at MyCommand-v5.7.4.jar/it.emmerrei.updater.Updater.access$0(Updater.java:718) ~[MyCommand-v5.7.4.jar:?]
	at MyCommand-v5.7.4.jar/it.emmerrei.updater.Updater$UpdateRunnable.run(Updater.java:714) ~[MyCommand-v5.7.4.jar:?]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
[17:31:43] [Server thread/INFO]: | You're running the latest version of MyCommand.
[17:31:43] [Server thread/INFO]: |          by emmerrei a.k.a. ivanfromitaly.           
[17:31:43] [Server thread/INFO]: *-=-=-=-=-=-=-=-=-=-*   Done!   *-=-=-=-=-=-=-=-=-=-=-*
[17:31:43] [Server thread/INFO]: [LPC] Enabling LPC v3.6.1
[17:31:43] [Server thread/INFO]: [SimpPay] Enabling SimpPay v1.2.4-BETA
[17:31:43] [packetevents-update-check-thread/INFO]: [packetevents] Checking for updates, please wait...
[17:31:43] [Server thread/INFO]: [SimpPay] Enabled floodgate support
[17:31:43] [Server thread/INFO]: [SimpPay] Loading all configurations
[17:31:43] [packetevents-update-check-thread/INFO]: [packetevents] There is an update available for PacketEvents! Your build: (2.8.0) | Latest release: (2.9.3)
[17:31:43] [Server thread/INFO]: [SimpPay] All configurations loaded successfully
[17:31:43] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-2 - Starting...
[17:31:44] [Server thread/INFO]: [com.zaxxer.hikari.pool.HikariPool] HikariPool-2 - Added connection conn1: url=jdbc:h2:file:/home/<USER>/plugins/SimpPay/simppay.db user=ROOT
[17:31:44] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-2 - Start completed.
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] creating table 'players'
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] executed create table statement changed 0 rows: CREATE TABLE IF NOT EXISTS `players` (`uuid` VARCHAR(48) , `name` VARCHAR(255) NOT NULL , PRIMARY KEY (`uuid`) ) 
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] creating table 'banking_payments'
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] executed create table statement changed 0 rows: CREATE TABLE IF NOT EXISTS `banking_payments` (`payment_id` VARCHAR(48) , `player_uuid` VARCHAR(48) NOT NULL , `amount` DOUBLE PRECISION NOT NULL , `timestamp` BIGINT NOT NULL , `ref_id` VARCHAR(255) , `api_provider` VARCHAR(100) NOT NULL , PRIMARY KEY (`payment_id`) ) 
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] creating table 'card_payments'
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] executed create table statement changed 0 rows: CREATE TABLE IF NOT EXISTS `card_payments` (`payment_id` VARCHAR(48) , `player_uuid` VARCHAR(48) NOT NULL , `pin` VARCHAR(255) NOT NULL , `serial` VARCHAR(255) NOT NULL , `price_value` DOUBLE PRECISION NOT NULL , `card_type` VARCHAR(100) NOT NULL , `ref_id` VARCHAR(255) NOT NULL , `true_amount` DOUBLE PRECISION NOT NULL , `amount` DOUBLE PRECISION NOT NULL , `timestamp` BIGINT NOT NULL , `api_provider` VARCHAR(100) NOT NULL , PRIMARY KEY (`payment_id`) ) 
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] creating table 'player_streak'
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] executed create table statement changed 0 rows: CREATE TABLE IF NOT EXISTS `player_streak` (`player_uuid` VARCHAR(48) , `last_recharge_date` TIMESTAMP , `current_streak` INTEGER , `claimed_today` BOOLEAN , PRIMARY KEY (`player_uuid`) ) 
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] creating table 'player_data'
[17:31:44] [Server thread/INFO]: [com.j256.ormlite.table.TableUtils] executed create table statement changed 0 rows: CREATE TABLE IF NOT EXISTS `player_data` (`id` INTEGER AUTO_INCREMENT , `player_uuid` VARCHAR(48) NOT NULL , `key` VARCHAR(255) NOT NULL , `value` VARCHAR(255) NOT NULL ,  UNIQUE (`player_uuid`), PRIMARY KEY (`id`) ) 
[17:31:44] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: simppay [1.0]
[17:31:44] [Server thread/WARN]: [SimpPay] Inventory Framework is running as a shaded non-relocated library. It's extremely recommended that you relocate the library package. Learn more about on docs: https://github.com/DevNatan/inventory-framework/wiki/Installation#preventing-library-conflicts
[17:31:44] [Server thread/INFO]: [LPX] Enabling LPX v3.5.12
[17:31:44] [Server thread/INFO]: [LPX] LPX enabled!
[17:31:44] [Server thread/INFO]: [LPX] License successfully validated!
[17:31:44] [Server thread/INFO]: [TaiXiu] Enabling TaiXiu v2.6
[17:31:44] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: taixiu [2.6]
[17:31:44] [Server thread/INFO]: --------------------------------
[17:31:44] [Server thread/INFO]:   _____           _    __  __  _         
[17:31:44] [Server thread/INFO]:  |_   _|   __ _  (_)   \ \/ / (_)  _   _ 
[17:31:44] [Server thread/INFO]:    | |    / _  | | |    \  /  | | | | | |
[17:31:44] [Server thread/INFO]:    | |   | (_| | | |    /  \  | | | |_| |
[17:31:44] [Server thread/INFO]:    |_|    \____| |_|   /_/\_\ |_|  \____|
[17:31:44] [Server thread/INFO]: 
[17:31:44] [Server thread/INFO]: Version: 2.6
[17:31:44] [Server thread/INFO]: Author: Cortez_Romeo
[17:31:44] [Server thread/INFO]: Khởi chạy plugin trên phiên bản: CraftServer
[17:31:44] [Server thread/INFO]: 
[17:31:44] [Server thread/INFO]: Support:
[17:31:44] [Server thread/INFO]: [YES] PlaceholderAPI
[17:31:44] [Server thread/INFO]: [YES] Floodgate API (Forms and Cumulus)
[17:31:44] [Server thread/INFO]: [YES] PlayerPoints
[17:31:44] [Server thread/INFO]: 
[17:31:44] [Server thread/INFO]: --------------------------------
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Enabling DeluxeCoinflip v2.9.6
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] 
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip]  __ __    DeluxeCoinflip v2.9.6
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] /  |_     Author: ItzSave
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] \_ |      (c) Zithium Studios 2021 - 2025. All rights reserved.
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] 
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Loading bStats metrics
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Registered economy provider 'Vault' using Vault plugin.
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Registered economy provider 'PlayerPoints' using PlayerPoints plugin.
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Registered economy provider 'CUSTOM_CURRENCY
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] [ACF] Enabled Asynchronous Tab Completion Support!
[17:31:44] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: deluxecoinflip [2.9.6]
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Hooked into PlaceholderAPI successfully
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] 
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] Successfully loaded in 103ms
[17:31:44] [Server thread/INFO]: [DeluxeCoinflip] 
[17:31:44] [Server thread/INFO]: [LiteSignIn] Enabling LiteSignIn v*******
[17:31:44] [Server thread/INFO]: [LiteSignIn] The language information file has been loaded.
[17:31:44] [Server thread/INFO]: [LiteSignIn] Enabled Command Listener .
[17:31:44] [Server thread/INFO]: [LiteSignIn] Enabled Event Listener .
[17:31:44] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: litesignin [1.0.0]
[17:31:44] [Server thread/INFO]: Điểm danh | Find the PlaceholderAPI and get ready.
[17:31:44] [Server thread/INFO]: Điểm danh | Async thread started.
[17:31:44] [Server thread/INFO]: Điểm danh | Plugin are enabled!
[17:31:44] [Server thread/INFO]: [InvSee++_Clear] Enabling InvSeePlusPlus_Clear v0.29.23-SNAPSHOT
[17:31:44] [Server thread/INFO]: [WorldGuard] Enabling WorldGuard v7.0.13+82fdc65
[17:31:45] [Server thread/INFO]: [WorldGuard] (world) TNT ignition is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world) Lighters are PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world) Lava fire is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world) Fire spread is UNRESTRICTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'world'
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_nether) TNT ignition is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_nether) Lighters are PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_nether) Lava fire is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_nether) Fire spread is UNRESTRICTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'world_nether'
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_the_end) TNT ignition is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_the_end) Lighters are PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_the_end) Lava fire is PERMITTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] (world_the_end) Fire spread is UNRESTRICTED.
[17:31:45] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'world_the_end'
[17:31:45] [Server thread/INFO]: [WorldGuard] Loading region data...
[17:31:45] [Server thread/INFO]: [ClansPlus] Enabling ClansPlus v1.7
[17:31:45] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: clanplus [1.7]
[17:31:45] [Server thread/INFO]: --------------------------------
[17:31:45] [Server thread/INFO]:   ____ _                 ____  _
[17:31:45] [Server thread/INFO]:  / ___| | __ _ _ __     |  _ \| |_   _ ___
[17:31:45] [Server thread/INFO]: | |   | |/ _` | '_ \    | |_) | | | | / __|
[17:31:45] [Server thread/INFO]: | |___| | (_| | | | |   |  __/| | |_| \__ \
[17:31:45] [Server thread/INFO]:  \____|_|\__,_|_| |_|   |_|   |_|\__,_|___/
[17:31:45] [Server thread/INFO]: 
[17:31:45] [Server thread/INFO]: Version: 1.7
[17:31:45] [Server thread/INFO]: Author: Cortez_Romeo
[17:31:45] [Server thread/INFO]: Khởi chạy plugin trên phiên bản: CraftServer
[17:31:45] [Server thread/INFO]: 
[17:31:45] [Server thread/INFO]: Support:
[17:31:45] [Server thread/INFO]: [SUPPORTED] Vault
[17:31:45] [Server thread/INFO]: [SUPPORTED] PlaceholderAPI
[17:31:45] [Server thread/INFO]: [SUPPORTED] PlayerPoints
[17:31:45] [Server thread/INFO]: [SUPPORTED] MythicMobs
[17:31:45] [Server thread/INFO]: 
[17:31:45] [Server thread/INFO]: --------------------------------
[17:31:45] [Server thread/INFO]: [UltraBar] Enabling UltraBar v*******
[17:31:45] [Server thread/INFO]: [UltraBar] WorldGuard detected. WorldGuard addon activated.
[17:31:45] [Server thread/INFO]: [UltraBar] PlaceholderAPI detected. PlaceholderAPI addon activated.
[17:31:45] [Server thread/INFO]: [UltraBar] Your server is running version 1.21.4!
[17:31:45] [Server thread/INFO]: [UltraBar] UltraBar is enabled and running fine! V: *******
[17:31:45] [Server thread/INFO]: [UltraBar] Bstat metrics for this plugin is enabled. Disable it in the config if you do not want it on.
[17:31:45] [Server thread/INFO]: [BeaconPlus] Enabling BeaconPlus3 v3.1.0
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: COMMAND_EXECUTOR
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: EXP_BOOST
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: FLY
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: FURNACE_BOOST
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: CROPS_BOOST
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: MAGNET
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: POTION_EFFECT
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: POTION_DURATION_BOOST
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: STUPID_AI
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: SPAWNER_BOOST
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: FIRE_CONTROL
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: SATURATION
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: COOLDOWN_REDUCTION
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: EXP_GAIN
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: IMMORTALITY_FIELD
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: PERMISSION
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: ATTRIBUTE_MODIFIER
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: PREVENT_MOB_SPAWNING
[17:31:45] [Server thread/INFO]: [BeaconPlus] Registered Effect Parser: KEEP_CHUNK_LOADED
[17:31:45] [Server thread/INFO]: [BeaconPlus] Uses Paper Entity Finder
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Using Modern Recipe Manager
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Unloaded 0 chunks (0 beacons)
[17:31:45] [Server thread/INFO]: [WorldGuardExtraFlags] Enabling WorldGuardExtraFlags v4.2.4-SNAPSHOT
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.TeleportOnEntryFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.TeleportOnExitFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.WalkSpeedFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.FlySpeedFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.FlyFlagHandler
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Uses FILE data storage
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Uses WORLD beacon data storage
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.GlideFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.GodmodeFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.PlaySoundsFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.BlockedEffectsFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.GiveEffectsFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.CommandOnEntryFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.CommandOnExitFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.ConsoleCommandOnEntryFlagHandler
[17:31:45] [Server thread/INFO]: [WorldGuard] Registering session handler net.goldtreeservers.worldguardextraflags.wg.handlers.ConsoleCommandOnExitFlagHandler
[17:31:45] [Server thread/INFO]: [Essentials] Enabling Essentials v2.21.0
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Added power source: GOLD_BLOCK (1.0)
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Added power source: IRON_BLOCK (1.0)
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Added power source: EMERALD_BLOCK (3.0)
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Added power source: DIAMOND_BLOCK (2.0)
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Added power source: NETHERITE_BLOCK (4.0)
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Uses Exponential Range calculator
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: activator
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: furnace
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: strength
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: invisible
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: regeneration
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: resist
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: fastdig
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: cure
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: crops
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: spawners
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: slowdown
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: speed
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: peaceful
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: nightvision
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: flying
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: exp_boost
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: luck
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: burner
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: water_breathing
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: fireExtinguisher
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: poison
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: gravity_well
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: jump
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: exp_gain
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: cooldown_reduction
[17:31:45] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded effect: immortality
[17:31:46] [Server thread/INFO]: [Essentials] Attempting to convert old kits in config.yml to new kits.yml
[17:31:46] [Server thread/INFO]: [Essentials] No kits found to migrate.
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Tick Count Provider as the provider for TickCountProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.12.2+ Player Locale Provider as the provider for PlayerLocaleProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Known Commands Provider as the provider for KnownCommandsProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.8.3+ Spawner Item Provider as the provider for SpawnerItemProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.14+ Sign Data Provider as the provider for SignDataProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Material Tag Provider as the provider for MaterialTagProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.13+ Spawn Egg Provider as the provider for SpawnEggProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.21+ InventoryView Interface ABI Provider as the provider for InventoryViewProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.17.1+ World Info Provider as the provider for WorldInfoProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Reflection Online Mode Provider as the provider for OnlineModeProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Container Provider as the provider for ContainerProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.20.6+ Potion Meta Provider as the provider for PotionMetaProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Serialization Provider as the provider for SerializationProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.20.4+ Damage Event Provider as the provider for DamageEventProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.14.4+ Persistent Data Container Provider as the provider for PersistentDataProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.20.5+ Banner Data Provider as the provider for BannerDataProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.12+ Spawner Block Provider as the provider for SpawnerBlockProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Server State Provider as the provider for ServerStateProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.21.4+ Sync Commands Provider as the provider for SyncCommandsProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected 1.11+ Item Unbreakable Provider as the provider for ItemUnbreakableProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Reflection Formatted Command Alias Provider as the provider for FormattedCommandAliasProvider
[17:31:46] [Server thread/INFO]: [Essentials] Selected Paper Biome Key Provider as the provider for BiomeKeyProvider
[17:31:46] [Server thread/INFO]: [Essentials] Loaded 43465 items from items.json.
[17:31:46] [Server thread/INFO]: [Essentials] Using locale en_US
[17:31:46] [Server thread/INFO]: [Essentials] ServerListPingEvent: Spigot iterator API
[17:31:46] [Server thread/INFO]: [Essentials] Starting Metrics. Opt-out using the global bStats config.
[17:31:46] [Server thread/INFO]: [Vault] [Economy] Essentials Economy hooked.
[17:31:46] [Server thread/INFO]: [Essentials] Using Vault based permissions (LuckPerms)
[17:31:46] [Server thread/INFO]: [HeadDatabase] Enabling HeadDatabase v4.21.2
[17:31:46] [Server thread/INFO]: [HeadDatabase] [Spigotunlocked.com] - COSMO
[17:31:46] [Server thread/INFO]: [HeadDatabase] §bUsing default §3"en_US.lang" §bcreated by §6Arcaniax
[17:31:46] [Server thread/WARN]: [HeadDatabase] Economy was not loaded, some features will be disabled!
[17:31:46] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: hdb [4.21.2]
[17:31:46] [Server thread/INFO]: [FancyHolograms] Enabling FancyHolograms v2.4.2
[17:31:46] [FancyLogger/INFO]: WorldHandle: class net.minecraft.server.level.ServerLevel[FancyHolograms] (Server thread) INFO: Successfully enabled FancyHolograms version 2.4.2
[17:31:46] [Server thread/INFO]: [Orestack] Enabling Orestack v3.28.1
[17:31:46] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 7 holograms for all loaded worlds
[17:31:46] [Server thread/INFO]: [Orestack] Found compatible server version: 1.21.4
[17:31:46] [Server thread/INFO]: [Orestack] Looking for compatible plugins...
[17:31:46] [Server thread/INFO]: [Orestack] Vault Hook: created successfully
[17:31:46] [Server thread/INFO]: [Orestack] PlaceholderAPI Hook: created successfully
[17:31:46] [Server thread/INFO]: [Orestack] Generating directories and files...
[17:31:46] [Server thread/INFO]: [Orestack] Generating example files...
[17:31:46] [Server thread/INFO]: [Orestack] Establishing database connection...
[17:31:46] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-3 - Starting...
[17:31:47] [Server thread/INFO]: [com.zaxxer.hikari.pool.HikariPool] HikariPool-3 - Added connection conn0: url=jdbc:h2:file:/home/<USER>/plugins/Orestack/data user=
[17:31:47] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-3 - Start completed.
[17:31:47] [Server thread/INFO]: [Orestack] Loading configuration and data...
[17:31:47] [FancyLogger/INFO]: [FancyHolograms] (ForkJoinPool.commonPool-worker-2) WARN: 
[17:31:47] [FancyLogger/INFO]: -------------------------------------------------------
[17:31:47] [FancyLogger/INFO]: You are not using the latest version of the FancyHolograms plugin.
[17:31:47] [FancyLogger/INFO]: Please update to the newest version (2.5.0).
[17:31:47] [FancyLogger/INFO]: https://modrinth.com/plugin/FancyHolograms
[17:31:47] [FancyLogger/INFO]: -------------------------------------------------------
[17:31:47] [FancyLogger/INFO]: 
[17:31:47] [Folia Async Scheduler Thread #1/INFO]: [HeadDatabase] Successfully loaded 87751 heads!
[17:31:47] [Folia Async Scheduler Thread #1/INFO]: [HeadDatabase] Successfully loaded 18 featured tags!
[17:31:47] [Folia Async Scheduler Thread #2/WARN]: [HeadDatabase] §cYou are using an outdated version!
[17:31:47] [Folia Async Scheduler Thread #2/WARN]: [HeadDatabase] Latest version: §a2021-05-25§e. You are on version: §c4.21.2§e.
[17:31:47] [Folia Async Scheduler Thread #2/WARN]: [HeadDatabase] Update here: §bhttps://www.spigotmc.org/resources/head-database.92692/
[17:31:47] [Server thread/ERROR]: Error occurred while enabling Orestack v3.28.1 (Is it up to date?)
java.lang.NullPointerException: Cannot invoke "org.bukkit.World.getName()" because "world" is null
	at Orestack[free]-3.28.1.jar/io.github.pigaut.orestack.generator.GeneratorManager.lambda$loadData$1(GeneratorManager.java:82) ~[Orestack[free]-3.28.1.jar:?]
	at io.github.pigaut.sql.database.statement.SimpleDatabaseStatement.lambda$fetchAllRows$14(SimpleDatabaseStatement.java:184) ~[?:?]
	at io.github.pigaut.sql.database.statement.SimpleDatabaseStatement.executeStatement(SimpleDatabaseStatement.java:208) ~[?:?]
	at io.github.pigaut.sql.database.statement.SimpleDatabaseStatement.fetchAllRows(SimpleDatabaseStatement.java:181) ~[?:?]
	at Orestack[free]-3.28.1.jar/io.github.pigaut.orestack.generator.GeneratorManager.loadData(GeneratorManager.java:71) ~[Orestack[free]-3.28.1.jar:?]
	at Orestack[free]-3.28.1.jar/io.github.pigaut.orestack.voxel.plugin.EnhancedJavaPlugin.onEnable(EnhancedJavaPlugin.java:154) ~[Orestack[free]-3.28.1.jar:?]
	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519) ~[leaf-api-1.21.4-R0.1-SNAPSHOT.jar:?]
	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
[17:31:47] [Server thread/INFO]: [Orestack] Disabling Orestack v3.28.1
[17:31:47] [Server thread/INFO]: [Orestack] Saving data to database...
[17:31:47] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-3 - Shutdown initiated...
[17:31:47] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-3 - Shutdown completed.
[17:31:47] [Server thread/INFO]: [LibsDisguises] Enabling LibsDisguises v11.0.5
[17:31:47] [Server thread/INFO]: [LibsDisguises] File Name: LibsDisguisesFree-S81-584794.jar
[17:31:47] [Server thread/INFO]: [LibsDisguises] Discovered nms version: (Package: {Not package relocated}) (LD: v1_21_R3) (MC: 1.21.4)
[17:31:47] [Server thread/INFO]: [LibsDisguises] Jenkins Build: #1585
[17:31:47] [Server thread/INFO]: [LibsDisguises] Build Date: 13/04/2025 03:40
[17:31:47] [Server thread/INFO]: [LibsDisguises] If you own the plugin, place the premium jar downloaded from https://www.spigotmc.org/resources/libs-disguises.32453/ in plugins/LibsDisguises/
[17:31:47] [Server thread/INFO]: [LibsDisguises] You are running the free version, commands limited to non-players and operators. (Console, Command Blocks, Admins)
[17:31:47] [Server thread/INFO]: [LibsDisguises] Config 'TallSelfDisguises' is set to 'SCALED', LD will scale down (when possible) oversized disguises from self disguise. https://www.spigotmc.org/wiki/lib-s-disguises-faq/#tall-disguises-self-disguises
[17:31:48] [Server thread/INFO]: [com.github.retrooper.packetevents.PacketEventsAPI] Loading block mappings for V_1_21_4/17...
[17:31:48] [Server thread/INFO]: [com.github.retrooper.packetevents.PacketEventsAPI] Finished loading block mappings for V_1_21_4/17 in 120.371526ms
[17:31:48] [Server thread/INFO]: [LibsDisguises] Loaded custom disguise libraryaddict
[17:31:48] [Server thread/INFO]: [LibsDisguises] Loaded custom disguise GwenCuti
[17:31:48] [Server thread/INFO]: [LibsDisguises] Loaded custom disguise LunaCutii
[17:31:48] [Server thread/INFO]: [LibsDisguises] Loaded 3 custom disguises
[17:31:48] [Server thread/INFO]: [LibsDisguises] Config is up to date!
[17:31:48] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: libsdisguises [1.0.0]
[17:31:48] [Server thread/INFO]: [LibsDisguises] PlaceholderAPI support enabled
[17:31:48] [Server thread/INFO]: [ModelEngine] [S] Compatibility applied: LibsDisguises
[17:31:48] [Server thread/INFO]: [ExcellentCrates] Enabling ExcellentCrates v6.2.2
[17:31:48] [Server thread/INFO]: [ExcellentCrates] Powered by nightcore
[17:31:49] [Server thread/WARN]: [ExcellentCrates] *************************
[17:31:49] [Server thread/WARN]: [ExcellentCrates] You don't have EconomyBridge installed.
[17:31:49] [Server thread/WARN]: [ExcellentCrates] The following features will be unavailable:
[17:31:49] [Server thread/WARN]: [ExcellentCrates] - Crate open cost.
[17:31:49] [Server thread/WARN]: [ExcellentCrates] - Custom item plugin support.
[17:31:49] [Server thread/WARN]: [ExcellentCrates] *************************
[17:31:49] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-4 - Starting...
[17:31:49] [Server thread/INFO]: [com.zaxxer.hikari.pool.HikariPool] HikariPool-4 - Added connection org.sqlite.jdbc4.JDBC4Connection@6b623449
[17:31:49] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-4 - Start completed.
[17:31:49] [Server thread/INFO]: [ExcellentCrates] Loaded 7 crate openings.
[17:31:49] [Server thread/INFO]: [ExcellentCrates] Loaded 19 crate keys.
[17:31:49] [Server thread/INFO]: [ExcellentCrates] Loaded 4 rarities!
[17:31:49] [Server thread/INFO]: [ExcellentCrates] Loaded 26 crates.
[17:31:49] [Server thread/ERROR]: [nightcore] Invalid material 'null'. Found in '/home/<USER>/plugins/ExcellentCrates/milestones.yml' -> 'Content.Priority.Item'.
[17:31:49] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: excellentcrates [6.2.2]
[17:31:49] [Server thread/INFO]: [ExcellentCrates] Plugin loaded in 334 ms!
[17:31:49] [Server thread/INFO]: [PlayerAuctions] Enabling PlayerAuctions v1.31.1
[17:31:49] [Server thread/INFO]: [PlayerAuctions] Vault found, now enabling PlayerAuctions...
[17:31:51] [Server thread/INFO]: [PlayerAuctions] Found 21 config files to load!
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Permissions plugin found! (LuckPerms)
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Economy plugin found! (EssentialsX Economy)
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Chat plugin found! (LuckPerms)
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Found PlaceholderAPI integrating support...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Found Vault Currency integrating support...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Found Product Converter integrating support...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Found Item Currency integrating support...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Found XP Currency integrating support...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] SQLite database is enabling...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Loading Metrics...
[17:31:56] [Server thread/INFO]: [PlayerAuctions] Successfully loaded Metrics!
[17:31:56] [Server thread/INFO]: [ajLeaderboards] Enabling ajLeaderboards v2.8.0
[17:31:56] [Server thread/INFO]: [ajLeaderboards] Using H2 flatfile for board cache. (h2)
[17:31:56] [Server thread/INFO]: [ajLeaderboards] Loaded 16 boards
[17:31:56] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ajlb [2.8.0]
[17:31:56] [Server thread/INFO]: [ajLeaderboards] PAPI placeholders successfully registered!
[17:31:56] [Server thread/INFO]: [ajLeaderboards] ajLeaderboards v2.8.0 by ajgeiss0702 enabled!
[17:31:56] [Server thread/INFO]: [SuperVanish] Enabling SuperVanish v6.2.20
[17:31:56] [Server thread/INFO]: [SuperVanish] Hooked into PaperSpigot for server list ping support
[17:31:56] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: supervanish [6.2.20]
[17:31:56] [Server thread/INFO]: [SuperVanish] Hooked into PlaceholderAPI
[17:31:56] [Server thread/INFO]: [SuperVanish] Hooked into Essentials
[17:31:56] [Server thread/INFO]: [InvSee++_Give] Enabling InvSeePlusPlus_Give v0.29.23-SNAPSHOT
[17:31:56] [Server thread/INFO]: [antiRedstoneClock] Enabling antiRedstoneClock v1.5.0
[17:31:56] [Server thread/WARN]: [antiRedstoneClock] PlotSquared hasn't been found!
[17:31:56] [Server thread/INFO]: [antiRedstoneClock] Plugin loaded in 3 ms
[17:31:56] [Server thread/INFO]: [GSit] Enabling GSit v2.3.2
[17:31:56] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: gsit [2.3.2]
[17:31:56] [Server thread/INFO]: [WorldGuard] Registering session handler dev.geco.gsit.link.worldguard.RegionFlagHandler
[17:31:56] [Server thread/INFO]: [GSit] The plugin was successfully enabled.
[17:31:56] [Server thread/INFO]: [GSit] Link with PlaceholderAPI successful!
[17:31:56] [Server thread/INFO]: [GSit] Link with WorldGuard successful!
[17:31:56] [Server thread/INFO]: [AxTrade] Enabling AxTrade v1.17.0
[17:31:57] [Server thread/INFO]: [AxTrade] Hooked into Vault!
[17:31:57] [Server thread/INFO]: [AxTrade] Hooked into PlayerPoints!
[17:31:57] [Server thread/INFO]: [AxTrade] Hooked into CoinsEngine!
[17:31:57] [Server thread/INFO]: [AxTrade] CoinsEngine currency named coins not found! Change the currency-name or disable the hook to get rid of this warning!
[17:31:57] [Server thread/INFO]: [AxTrade] CoinsEngine currency named money not found! Change the currency-name or disable the hook to get rid of this warning!
[17:31:57] [Server thread/INFO]: [AxTrade] Loaded plugin!
[17:31:57] [Server thread/INFO]: [Skript] Enabling Skript v2.11.2
[17:31:58] [Server thread/INFO]: [Skript]  ~ created by & © Peter Güttinger aka Njol ~
[17:31:58] [Server thread/INFO]: [Citizens] Enabling Citizens v2.0.37-SNAPSHOT (build 3760)
[17:31:58] [Server thread/INFO]: [Citizens] Using mojmapped server, avoiding server package checks
[17:31:58] [Server thread/INFO]: [Citizens] Loaded 0 templates.
[17:31:58] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: citizensplaceholder [1.0.0]
[17:31:58] [Server thread/INFO]: [Citizens] Loaded economy handling via Vault.
[17:31:58] [Server thread/INFO]: [SuperVanish] Hooked into Citizens
[17:31:58] [Server thread/INFO]: [ModelEngine] [S] Compatibility applied: Citizens
[17:31:58] [Server thread/INFO]: [TAB] Enabling TAB v5.2.0
[17:31:58] [Server thread/INFO]: [TAB] Loaded NMS hook in 4ms
[17:31:58] [ForkJoinPool.commonPool-worker-3/INFO]: [Skript] Loaded 5442 aliases in 349ms
[17:31:58] [ForkJoinPool.commonPool-worker-2/INFO]: [Skript] A new version of Skript is available: 2.12.0 (you're currently running 2.11.2)
[17:31:58] [ForkJoinPool.commonPool-worker-2/INFO]: Download it at: https://github.com/SkriptLang/Skript/releases/download/2.12.0/Skript-2.12.0.jar
[17:31:58] [Server thread/INFO]: [TAB] [WARN] [config.yml] layout.layouts.default: Layout default has invalid fixed slot defined as "". Supported values are "SLOT|TEXT" and "SLOT|TEXT|SKIN", where SLOT is a number from 1 to 80, TEXT is displayed text and SKIN is skin used for the slot
[17:31:58] [Server thread/INFO]: [TAB] [Hint] [config.yml] Layout feature automatically includes prevent-spectator-effect, therefore the feature can be disabled for better performance, as it is not needed at all (assuming it is configured to always display some layout).
[17:31:58] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: tab [5.2.0]
[17:31:58] [Server thread/INFO]: [TAB] [WARN] Found a total of 1 issues.
[17:31:58] [Server thread/INFO]: [TAB] Enabled in 123ms
[17:31:58] [Server thread/INFO]: [EssentialsSpawn] Enabling EssentialsSpawn v2.21.0
[17:31:58] [Server thread/INFO]: [EssentialsSpawn] Starting Metrics. Opt-out using the global bStats config.
[17:31:58] [Server thread/INFO]: [DecentHolograms] Enabling DecentHolograms v2.8.17
[17:31:58] [Server thread/INFO]: [skript-placeholders] Enabling skript-placeholders v1.7.0
[17:31:58] [Server thread/INFO]: [HeadBlocks] Enabling HeadBlocks v2.6.15
[17:31:58] [Server thread/INFO]: HeadBlocks initializing...
[17:31:58] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: headblocks [1.0.0]
[17:31:58] [Server thread/INFO]: SQLite storage properly connected!
[17:31:58] [Server thread/INFO]: Loaded 1 (+0 HeadDatabase heads) configuration heads!
[17:31:58] [Server thread/INFO]: Loaded 20 locations!
[17:31:58] [Server thread/INFO]: HeadBlocks successfully loaded!
[17:31:58] [Server thread/INFO]: [BetterFarming] Enabling BetterFarming v5.10.4
[17:31:58] [Server thread/INFO]: [BetterFarming] Version: 5.10.4 Previous: 5.10.4
[17:31:58] [Server thread/INFO]: [BetterFarming] Server: 1.21.4 running Paper
[17:31:58] [Server thread/INFO]: [BetterFarming] Licensed to: 315463
[17:31:58] [Server thread/INFO]: [BetterFarming] Experiencing issues or having questions? Join our Discord!
[17:31:58] [Server thread/INFO]: [BetterFarming] Discord: https://discord.incredibleplugins.com
[17:31:58] [Server thread/INFO]: [BetterFarming] Wiki: https://wiki.incredibleplugins.com/betterfarming
[17:31:58] [Server thread/INFO]: [BetterFarming]  
[17:31:58] [Server thread/INFO]: [BetterFarming] [Integrations] Successfully integrated DecentHolograms as hologram manager.
[17:31:58] [Server thread/INFO]: [BetterFarming] [Integrations] Added support for the vanish feature of the plugin SuperVanish.
[17:31:58] [Server thread/INFO]: [BetterFarming] Using SQLite as database driver.
[17:31:58] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-5 - Starting...
[17:31:59] [Server thread/INFO]: [com.zaxxer.hikari.HikariDataSource] HikariPool-5 - Start completed.
[17:31:59] [Server thread/INFO]: [BetterFarming] Successful connected to SQL database.
[17:31:59] [Server thread/INFO]: [BetterFarming] Database is ready.
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling SuperiorSkyblock2 v2024.4-b445
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block ACACIA_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block ACACIA_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block ACACIA_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block ACACIA_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_BUTTON
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_FENCE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_FENCE_GATE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_PRESSURE_PLATE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BAMBOO_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BEE_NEST
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BIRCH_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BIRCH_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BIRCH_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BIRCH_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BLACK_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BLUE_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block BROWN_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CAVE_VINES
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CAVE_VINES_PLANT
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_BUTTON
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_FENCE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_FENCE_GATE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_PRESSURE_PLATE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHERRY_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CHISELED_BOOKSHELF
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block COMPOSTER
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CRAFTER
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CRIMSON_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CRIMSON_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CRIMSON_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CRIMSON_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block CYAN_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block DARK_OAK_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block DARK_OAK_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block DARK_OAK_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block DARK_OAK_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block DECORATED_POT
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block EXPOSED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block EXPOSED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block GRAY_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block GREEN_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block JUNGLE_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block JUNGLE_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block JUNGLE_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block JUNGLE_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block LECTERN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block LIGHT_BLUE_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block LIGHT_GRAY_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block LIME_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block MAGENTA_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block MANGROVE_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block MANGROVE_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block MANGROVE_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block MANGROVE_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OAK_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OAK_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OAK_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OAK_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block ORANGE_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OXIDIZED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block OXIDIZED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block PINK_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_AZALEA_BUSH
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_BAMBOO
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_CORNFLOWER
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_CRIMSON_FUNGUS
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_CRIMSON_ROOTS
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_FLOWERING_AZALEA_BUSH
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_LILY_OF_THE_VALLEY
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_MANGROVE_PROPAGULE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_WARPED_FUNGUS
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_WARPED_ROOTS
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block POTTED_WITHER_ROSE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block PURPLE_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block RED_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block SPRUCE_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block SPRUCE_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block SPRUCE_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block SPRUCE_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WARPED_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WARPED_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WARPED_WALL_HANGING_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WARPED_WALL_SIGN
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_EXPOSED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_EXPOSED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_OXIDIZED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_OXIDIZED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_WEATHERED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WAXED_WEATHERED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WEATHERED_COPPER_DOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WEATHERED_COPPER_TRAPDOOR
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block WHITE_CANDLE
[17:31:59] [Server thread/WARN]: [SuperiorSkyblock2] Potentially missing interactable block YELLOW_CANDLE
[17:31:59] [Server thread/INFO]: Preparing start region for dimension minecraft:superiorworld_nether
[17:31:59] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:59] [Server thread/INFO]: Time elapsed: 35 ms
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_nether) TNT ignition is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_nether) Lighters are PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_nether) Lava fire is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_nether) Fire spread is UNRESTRICTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'SuperiorWorld_nether'
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world SuperiorWorld_nether
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world SuperiorWorld_nether
[17:31:59] [Server thread/INFO]: Preparing start region for dimension minecraft:superiorworld_the_end
[17:31:59] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:59] [Server thread/INFO]: Time elapsed: 26 ms
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_the_end) TNT ignition is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_the_end) Lighters are PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_the_end) Lava fire is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld_the_end) Fire spread is UNRESTRICTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'SuperiorWorld_the_end'
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world SuperiorWorld_the_end
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world SuperiorWorld_the_end
[17:31:59] [Server thread/INFO]: Preparing start region for dimension minecraft:superiorworld
[17:31:59] [Server thread/INFO]: Preparing spawn area: 0%
[17:31:59] [Server thread/INFO]: Time elapsed: 30 ms
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld) TNT ignition is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld) Lighters are PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld) Lava fire is PERMITTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] (SuperiorWorld) Fire spread is UNRESTRICTED.
[17:31:59] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'SuperiorWorld'
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world SuperiorWorld
[17:31:59] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world SuperiorWorld
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling the module bank...
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Finished enabling the module bank (Took 3ms)
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling the module missions...
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission miner_1
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission miner_2
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission miner_4
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission miner_3
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission miner_5
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission slayer_1
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission slayer_4
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission slayer_3
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission slayer_2
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission farmer_5
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission farmer_1
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission farmer_3
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission farmer_2
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission farmer_4
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission fisherman_1
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission fisherman_3
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission fisherman_2
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission explorer_1
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Registered mission explorer_2
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Finished enabling the module missions (Took 343ms)
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling the module nashorn-engine...
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Finished enabling the module nashorn-engine (Took 7ms)
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling the module generators...
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Finished enabling the module generators (Took 0ms)
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Enabling the module upgrades...
[17:31:59] [Server thread/INFO]: [SuperiorSkyblock2] Finished enabling the module upgrades (Took 15ms)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Loading messages started...
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] The language en-US-old is invalid, skipping...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2]  - Found 702 messages in the language files.
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Loading messages done (Took 66ms)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic winter_the_end.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic mycel_the_end.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic warped_nether.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic warped.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic desert.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic normal.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic normal_the_end.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic desert_nether.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic mycel_nether.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic winter.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic winter_nether.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic warped_the_end.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic normal_nether.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic desert_the_end.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully loaded schematic mycel.schematic (DefaultSchematicParser)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] SchematicsManagerImpl::parseSchematic SchematicsManagerImpl {parseSchematic} {ENTER} {3c6fd53acad28603} {3c6fd53acad28603}
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] An unexpected error occurred while loading schematic:
[17:32:00] [Server thread/WARN]: java.util.zip.ZipException: Not in GZIP format
[17:32:00] [Server thread/WARN]: 	at java.base/java.util.zip.GZIPInputStream.readHeader(GZIPInputStream.java:176)
[17:32:00] [Server thread/WARN]: 	at java.base/java.util.zip.GZIPInputStream.<init>(GZIPInputStream.java:79)
[17:32:00] [Server thread/WARN]: 	at java.base/java.util.zip.GZIPInputStream.<init>(GZIPInputStream.java:91)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.world.schematic.SchematicsManagerImpl.parseSchematic(SchematicsManagerImpl.java:346)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.world.schematic.SchematicsManagerImpl.loadFromFile(SchematicsManagerImpl.java:372)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.world.schematic.SchematicsManagerImpl.loadData(SchematicsManagerImpl.java:90)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.reloadPlugin(SuperiorSkyblockPlugin.java:483)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.onEnable(SuperiorSkyblockPlugin.java:260)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300)
[17:32:00] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1570)
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [biomes.yml] Biome 'WOODED_HILLS' is not valid, skipping...
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] [menus/permissions.yml] Couldn't convert permissions.set_biome.permission-enabled into an itemstack. Check type & data sections!
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] [menus/permissions.yml] Couldn't convert permissions.set_biome.permission-disabled into an itemstack. Check type & data sections!
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] [menus/permissions.yml] Couldn't convert permissions.set_biome.role-permission into an itemstack. Check type & data sections!
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [upgrades.yml] The item of the upgrade generator-rates (level 1) is not inside the pattern, skipping...
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [upgrades.yml] The item of the upgrade generator-rates (level 2) is not inside the pattern, skipping...
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [upgrades.yml] The item of the upgrade generator-rates (level 3) is not inside the pattern, skipping...
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [upgrades.yml] The item of the upgrade generator-rates (level 4) is not inside the pattern, skipping...
[17:32:00] [Server thread/WARN]: [SuperiorSkyblock2] [player-language.yml] The language vi-VN is not valid.
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Trying to connect to local database (SQLite)...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Successfully established connection with local database!
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Creating a backup file...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Backup done!
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Starting to load players...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] SQLDatabaseBridge::loadAllObjects ENTER {players_settings}
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] An unexpected error occurred while loading data from database:
[17:32:00] [Server thread/WARN]: java.lang.IllegalArgumentException: Locale vi_VN is not a valid locale.
[17:32:00] [Server thread/WARN]: 	at com.google.common.base.Preconditions.checkArgument(Preconditions.java:143)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.player.builder.SuperiorPlayerBuilderImpl.setLocale(SuperiorPlayerBuilderImpl.java:95)
[17:32:00] [Server thread/WARN]: 	at java.base/java.util.Optional.ifPresent(Optional.java:178)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.serialization.PlayersDeserializer.lambda$deserializePlayerSettings$1(PlayersDeserializer.java:75)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLDatabaseBridge.lambda$loadAllObjects$0(SQLDatabaseBridge.java:37)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.QueryResult.complete(QueryResult.java:35)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.impl.SQLiteSession.executeQuery(SQLiteSession.java:202)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.impl.SQLiteSession.select(SQLiteSession.java:156)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLHelper.select(SQLHelper.java:81)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLDatabaseBridge.loadAllObjects(SQLDatabaseBridge.java:34)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.serialization.PlayersDeserializer.deserializePlayerSettings(PlayersDeserializer.java:60)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.DataManager.loadPlayers(DataManager.java:122)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.DataManager.loadData(DataManager.java:59)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.reloadPlugin(SuperiorSkyblockPlugin.java:489)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.onEnable(SuperiorSkyblockPlugin.java:260)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300)
[17:32:00] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1570)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] SQLDatabaseBridge::loadAllObjects ENTER {players_settings}
[17:32:00] [Server thread/ERROR]: [SuperiorSkyblock2] An unexpected error occurred while loading data from database:
[17:32:00] [Server thread/WARN]: java.lang.IllegalArgumentException: Locale vi_VN is not a valid locale.
[17:32:00] [Server thread/WARN]: 	at com.google.common.base.Preconditions.checkArgument(Preconditions.java:143)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.player.builder.SuperiorPlayerBuilderImpl.setLocale(SuperiorPlayerBuilderImpl.java:95)
[17:32:00] [Server thread/WARN]: 	at java.base/java.util.Optional.ifPresent(Optional.java:178)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.serialization.PlayersDeserializer.lambda$deserializePlayerSettings$1(PlayersDeserializer.java:75)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLDatabaseBridge.lambda$loadAllObjects$0(SQLDatabaseBridge.java:37)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.QueryResult.complete(QueryResult.java:35)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.impl.SQLiteSession.executeQuery(SQLiteSession.java:202)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.session.impl.SQLiteSession.select(SQLiteSession.java:156)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLHelper.select(SQLHelper.java:81)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.sql.SQLDatabaseBridge.loadAllObjects(SQLDatabaseBridge.java:34)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.serialization.PlayersDeserializer.deserializePlayerSettings(PlayersDeserializer.java:60)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.DataManager.loadPlayers(DataManager.java:122)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.core.database.DataManager.loadData(DataManager.java:59)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.reloadPlugin(SuperiorSkyblockPlugin.java:489)
[17:32:00] [Server thread/WARN]: 	at SuperiorSkyblock2-2024.4-b445.jar//com.bgsoftware.superiorskyblock.SuperiorSkyblockPlugin.onEnable(SuperiorSkyblockPlugin.java:260)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.java.JavaPlugin.setEnabled(JavaPlugin.java:280)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginInstanceManager.enablePlugin(PaperPluginInstanceManager.java:202)
[17:32:00] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.enablePlugin(PaperPluginManagerImpl.java:109)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.enablePlugin(SimplePluginManager.java:519)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugin(CraftServer.java:676)
[17:32:00] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.enablePlugins(CraftServer.java:625)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadWorld0(MinecraftServer.java:746)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.loadLevel(MinecraftServer.java:491)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.dedicated.DedicatedServer.initServer(DedicatedServer.java:389)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1187)
[17:32:00] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300)
[17:32:00] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1570)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Finished loading 852 players (Took 28ms)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Starting to load islands...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Finished loading 494 islands (Took 290ms)
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Starting to load grid...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Finished grid!
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Starting to load stacked blocks...
[17:32:00] [Server thread/INFO]: [SuperiorSkyblock2] Finished stacked blocks!
[17:32:01] [Server thread/INFO]: [SuperiorSkyblock2] 
[17:32:01] [Server thread/INFO]: [SuperiorSkyblock2] A new version is available (v2025.1)!
[17:32:01] [Server thread/INFO]: [SuperiorSkyblock2] Version's description: "Adds support for 1.21.5, bug fixes and more!"
[17:32:01] [Server thread/INFO]: [SuperiorSkyblock2] 
[17:32:01] [Server thread/INFO]: [Multiverse-Core] Enabling Multiverse-Core v4.3.16
[17:32:01] [Server thread/WARN]: [Multiverse-Core] "Multiverse-Core v4.3.16" has registered a listener for org.bukkit.event.entity.EntityCreatePortalEvent on method "public void com.onarandombox.MultiverseCore.listeners.MVPortalListener.entityPortalCreate(org.bukkit.event.entity.EntityCreatePortalEvent)", but the event is Deprecated. "Server performance will be affected"; please notify the authors [dumptruckman, Rigby, fernferret, lithium3141, main--].
[17:32:01] [Server thread/INFO]: [Multiverse-Core] §aWe are aware of the warning about the deprecated event. There is no alternative that allows us to do what we need to do and performance impact is negligible. It is safe to ignore.
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:flatroom
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 46 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (flatroom) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (flatroom) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (flatroom) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (flatroom) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'flatroom'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world flatroom
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world flatroom
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:survival
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 87 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (survival) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (survival) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (survival) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (survival) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'survival'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world survival
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world survival
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:dungeon
[17:32:01] [Server thread/INFO]: Time elapsed: 1 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (dungeon) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (dungeon) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (dungeon) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (dungeon) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'dungeon'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world dungeon
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world dungeon
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:sundayboss
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 99 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (sundayboss) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (sundayboss) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (sundayboss) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (sundayboss) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'sundayboss'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world sundayboss
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world sundayboss
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:spawn
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 127 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (spawn) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (spawn) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (spawn) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (spawn) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'spawn'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world spawn
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 5 holograms for world spawn
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:summerspawn
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 77 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (summerspawn) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (summerspawn) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (summerspawn) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (summerspawn) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'summerspawn'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world summerspawn
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world summerspawn
[17:32:01] [Server thread/INFO]: Preparing start region for dimension minecraft:boss1
[17:32:01] [Server thread/INFO]: Preparing spawn area: 0%
[17:32:01] [Server thread/INFO]: Time elapsed: 81 ms
[17:32:01] [Server thread/INFO]: [WorldGuard] (boss1) TNT ignition is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (boss1) Lighters are PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (boss1) Lava fire is PERMITTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] (boss1) Fire spread is UNRESTRICTED.
[17:32:01] [Server thread/INFO]: [WorldGuard] Loaded configuration for world 'boss1'
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loading holograms for world boss1
[17:32:01] [FancyLogger/INFO]: [FancyHolograms] (FancyHolograms-Holograms) INFO: Loaded 0 holograms for world boss1
[17:32:02] [Server thread/INFO]: [Multiverse-Core] 13 - World(s) loaded.
[17:32:02] [Server thread/WARN]: [Multiverse-Core] Buscript failed to load! The script command will be disabled! If you would like not to see this message, use `/mv conf enablebuscript false` to disable Buscript from loading.
[17:32:02] [Server thread/INFO]: [Multiverse-Core] Version 4.3.16 (API v24) Enabled - By dumptruckman, Rigby, fernferret, lithium3141 and main--
[17:32:02] [Server thread/INFO]: [CoreTools] Enabling CoreTools v1.2-SNAPSHOT
[17:32:02] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: coretools [1.2-SNAPSHOT]
[17:32:02] [Server thread/INFO]: [CoreTools] Hooked onto PlaceholderAPI
[17:32:02] [Server thread/INFO]: [Shopkeepers] Enabling Shopkeepers v2.23.8
[17:32:02] [Server thread/INFO]: [Shopkeepers] Citizens found: Enabling NPC shopkeepers.
[17:32:03] [Server thread/INFO]: [Shopkeepers] Loading the data of 292 shopkeepers ...
[17:32:04] [Server thread/INFO]: [UltimateKoth] Enabling UltimateKoth v2.13.0
[17:32:04] [Server thread/INFO]: [UltimateKoth] ========================================
[17:32:04] [Server thread/INFO]: [UltimateKoth]   _    _ _ _   _                 _       _  __     _   _     
[17:32:04] [Server thread/INFO]: [UltimateKoth]  | |  | | | | (_)               | |     | |/ /    | | | |  V2
[17:32:04] [Server thread/INFO]: [UltimateKoth]  | |  | | | |_ _ _ __ ___   __ _| |_ ___| ' / ___ | |_| |__  
[17:32:04] [Server thread/INFO]: [UltimateKoth]  | |  | | | __| | '_ ` _ \ / _` | __/ _ \  < / _ \| __| '_ \ 
[17:32:04] [Server thread/INFO]: [UltimateKoth]  | |__| | | |_| | | | | | | (_| | ||  __/ . \ (_) | |_| | | |
[17:32:04] [Server thread/INFO]: [UltimateKoth]   \____/|_|\__|_|_| |_| |_|\__,_|\__\___|_|\_\___/ \__|_| |_|
[17:32:04] [Server thread/INFO]: [UltimateKoth]                                                              
[17:32:04] [Server thread/INFO]: [UltimateKoth] Licensed to: ppkntw (38667)
[17:32:04] [Server thread/INFO]: [UltimateKoth] Language: EN (default)
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup database: SQLITE (Medium)
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Koth database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - LootBlocks database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Commands database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Loots database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Scheduler database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Hologram database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Stats database [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Scoreboard system [OK]
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup hooks...
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - PlaceholderAPI
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - ProtocolLib
[17:32:04] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: ukoth [UKoth V1]
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup economy...
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Vault
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - PlayerPoints
[17:32:04] [Server thread/INFO]: [UltimateKoth] Economy: VAULT
[17:32:04] [pool-145-thread-1/INFO]: [UltimateKoth] Loaded 0 LootBlocks(s).
[17:32:04] [pool-146-thread-1/INFO]: [UltimateKoth] Loaded 0 Loots(s).
[17:32:04] [pool-147-thread-1/INFO]: [UltimateKoth] Loaded 1 Scheduler(s).
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup Addons...
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup Extensions...
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - Hologram implementation: DecentHolograms (2.8.17)
[17:32:04] [Server thread/WARN]: [UltimateKoth]  ! Disabled: [SuperiorSkyblock2, BlueMap, TAB,]
[17:32:04] [Server thread/INFO]: [UltimateKoth] Setup koth modes...
[17:32:04] [Server thread/INFO]: [UltimateKoth]  - KOTH (default)
[17:32:04] [Server thread/INFO]: [UltimateKoth] Timezone date: (Asia/Ho_Chi_Minh) 7/20/25, 5:32 PM
[17:32:04] [Server thread/INFO]: [UltimateKoth] ========================================
[17:32:04] [Server thread/INFO]: [AdvancedOreGen] Enabling AdvancedOreGen v1.6.69-SNAPSHOT
[17:32:04] [Server thread/INFO]: [AdvancedOreGen] Using the vanilla hook! (Configured in config.yml)
[17:32:04] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: oregen [1.6.69-SNAPSHOT]
[17:32:04] [Server thread/INFO]: [DiscordSRV] Enabling DiscordSRV v1.29.0
[17:32:04] [Server thread/INFO]: [SCore] Enabling SCore v*********
[17:32:04] [Server thread/INFO]: SCore The library part of SCore is initializing ... (by SCore)
[17:32:04] [Server thread/INFO]: SCore ExecutableEvents hooked !  (*********) Load After
[17:32:04] [Server thread/INFO]: SCore PlaceholderAPI hooked !  (2.11.6)  Load Before
[17:32:04] [Server thread/INFO]: SCore WorldGuard hooked !  (7.0.13+82fdc65)  Load Before
[17:32:04] [Server thread/INFO]: SCore Vault hooked !  (1.7.3-b131)  Load Before
[17:32:04] [Server thread/INFO]: SCore SuperiorSkyblock2 hooked !  (2024.4-b445)  Load Before
[17:32:04] [Server thread/INFO]: SCore Multiverse-Core hooked !  (4.3.16)  Load Before
[17:32:04] [Server thread/INFO]: SCore packetevents hooked !  (2.8.0)  Load Before
[17:32:04] [Server thread/INFO]: SCore ProtocolLib hooked !
[17:32:04] [Server thread/INFO]: SCore Locale setup: EN
[17:32:04] [Server thread/INFO]: SCore NBTAPI hooked !  (2.14.1)  Load Before
[17:32:04] [Server thread/INFO]: SCore HeadDatabase hooked !  (4.21.2)  Load Before
[17:32:04] [Server thread/INFO]: SCore MythicMobs hooked !  (5.9.2-SNAPSHOT-538a490c) Load After
[17:32:04] [Server thread/INFO]: SCore DecentHolograms hooked !  (2.8.17)  Load Before
[17:32:04] [Server thread/INFO]: SCore ItemsAdder hooked !  (4.0.11) Load After
[17:32:04] [Server thread/INFO]: SCore ShopGUIPlus hooked !  (1.106.1) Load After
[17:32:04] [Server thread/INFO]: SCore RoseLoot hooked !  (1.3.0) Load After
[17:32:04] [Server thread/INFO]: SCore RoseStacker hooked !  (1.5.33) Load After
[17:32:04] [Server thread/INFO]: SCore TAB hooked !  (5.2.0)  Load Before
[17:32:04] [Server thread/INFO]: SCore WorldEdit hooked !  (2.13.1-SNAPSHOT-1075;fdc9d6d)  Load Before
[17:32:04] [Server thread/INFO]: SCore Language of the editor setup on EN
[17:32:04] [Server thread/INFO]: SCore will connect to the database hosted: In Local
[17:32:04] [Server thread/INFO]: SCore Connection to the db...
[17:32:04] [Server thread/INFO]: SCore will connect to the database hosted: In Local
[17:32:04] [Server thread/INFO]: SCore Creating table SecurityOP if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table Commands if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table Cooldowns if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table Commands Player if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table Commands Entity if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table Commands Block if not exists...
[17:32:04] [Server thread/INFO]: SCore Creating table UsePerDay if not exists...
[17:32:04] [Server thread/INFO]: SCore SCore loaded 0 delayed commands saved
[17:32:04] [Server thread/INFO]: ================ SCore ================
[17:32:04] [Server thread/INFO]: SCore is running on Folia
[17:32:04] [Server thread/INFO]: SCore is running on Paper or fork
[17:32:04] [Server thread/INFO]: SCore Version of the server 1.21.4-496-5311ae8 (MC: 1.21.4) !
[17:32:04] [Server thread/INFO]: SCore Language for in-game messages setup on EN
[17:32:05] [Server thread/INFO]: SCore SCore loaded 1 variables from local files !
[17:32:05] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: SCore [1.0.0]
[17:32:05] [Server thread/INFO]: ================ SCore ================
[17:32:05] [Server thread/INFO]: [MythicMobs] Enabling MythicMobs v5.9.2-SNAPSHOT-538a490c
[17:32:05] [Server thread/INFO]: [MythicMobs] Loading MythicMobs for Paper (MC: 1.21.4)...
[17:32:05] [Server thread/INFO]: [MythicMobs] The server is running Paper; enabled Paper exclusive functionality
[17:32:05] [DiscordSRV - Initialization/INFO]: [DiscordSRV] [JDA] Login Successful!
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic Citizens Support has been enabled!
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic LibsDisguises Support has been enabled!
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic MMOItems Support has been enabled!
[17:32:06] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mythic [5.0.0]
[17:32:06] [JDA MainWS-ReadThread/INFO]: [DiscordSRV] [JDA] Connected to WebSocket
[17:32:06] [pool-150-thread-1/INFO]: [DiscordSRV] DiscordSRV is up-to-date. (9d4734818ab27069d76f264a4cda74a699806770)
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic PlaceholderAPI Support has been enabled!
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic Vault Support has been enabled!
[17:32:06] [Server thread/INFO]: [MythicMobs] Mythic WorldGuard Support has been enabled!
[17:32:06] [Server thread/INFO]: [MythicMobs] Base directory /home/<USER>/plugins/MythicMobs/data
[17:32:06] [Server thread/INFO]: [MythicMobs] Module directory /home/<USER>/plugins/MythicMobs/data/worlds
[17:32:07] [JDA MainWS-ReadThread/INFO]: [DiscordSRV] [JDA] Finished Loading!
[17:32:07] [DiscordSRV - Initialization/INFO]: [DiscordSRV] Console channel ID was invalid, not forwarding console output
[17:32:07] [DiscordSRV - Initialization/INFO]: [DiscordSRV] Enabling Essentials hook
[17:32:07] [DiscordSRV - Initialization/INFO]: [DiscordSRV] Enabling SuperVanish hook
[17:32:07] [DiscordSRV - Initialization/INFO]: [DiscordSRV] Enabling LuckPerms hook
[17:32:07] [DiscordSRV - Initialization/INFO]: [DiscordSRV] Enabling PlaceholderAPI hook
[17:32:07] [Server thread/INFO]: [MythicMobs] Loading Packs...
[17:32:07] [Server thread/INFO]: [MythicMobs] Loading Items...
[17:32:07] [Server thread/INFO]: [MythicMobs] Loading Item Groups...
[17:32:07] [Server thread/INFO]: [MythicMobs] Loading Skills...
[17:32:08] [Server thread/ERROR]: [MythicMobs] Error loading LineConfig: Unbalanced Braces
[17:32:08] [Server thread/ERROR]: [MythicMobs] [Line]: a=1;r=250;spacing=6}]
[17:32:08] [Server thread/INFO]: [MythicMobs] Loading Drop Tables...
[17:32:08] [Server thread/INFO]: [MythicMobs] Loading Random Spawns...
[17:32:08] [Server thread/INFO]: [MythicMobs] Loading Spawn Blocks...
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Skill: Luna_E_Temp | File: /home/<USER>/plugins/MythicMobs/skills/World_Boss_Skills.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill Luna_E
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=Luna_E}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: Sonetto | File: /home/<USER>/plugins/MythicMobs/mobs/Summer_Dungeon_Boss.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxTimerBegin
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxTimerBegin}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: hoathechilinh | File: /home/<USER>/plugins/MythicMobs/mobs/hoathechilinh.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill aura_honghainhi
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=aura_honghainhi}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxTimerBegin
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxTimerBegin}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxFirstSkill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxFirstSkill}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxSecondSkill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxSecondSkill}
[17:32:08] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxThirdSkill
[17:32:08] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxThirdSkill}
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 272 mobs.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 4 vanilla mob overrides.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 0 mob stacks.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 1315 skills.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 47 random spawns.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 242 mythic items.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 22 drop tables.
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ Loaded 33 mob spawners.
[17:32:08] [Server thread/INFO]: [MythicMobs] MythicMobs configuration file loaded successfully.
[17:32:08] [Server thread/INFO]: [MythicMobs] Started up bStats Metrics
[17:32:08] [Server thread/INFO]: [MythicMobs] ✓ MythicMobs Premium v5.9.2 ( build 538a490c ) has been successfully loaded!
[17:32:09] [Server thread/INFO]: [MythicMobs] Model Engine Compatibility Loaded.
[17:32:09] [Server thread/INFO]: [ModelEngine] [S] Compatibility applied: MythicMobs
[17:32:09] [Server thread/INFO]: [Quests] Enabling Quests v5.2.2-b533
[17:32:09] [Server thread/INFO]: [Quests] Loaded language en-US. Translations via Crowdin
[17:32:09] [Server thread/INFO]: [Quests] Successfully linked Quests with Citizens
[17:32:09] [Server thread/INFO]: [Quests] Loading storage implementation: YAML
[17:32:09] [Server thread/INFO]: [BattlePass] Enabling BattlePass v4.9.15
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the event-easter quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-2 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-7 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-1 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-6 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-3 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-4 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-5 quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the week-pool quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [BattlePass] Finished loading the daily quests. All quests loaded successfully.
[17:32:09] [Server thread/INFO]: [Citizens] Hooked into Citizens
[17:32:09] [Server thread/INFO]: [ExcellentCrates] Hooked into ExcellentCrates
[17:32:09] [Server thread/INFO]: [MythicMobs] Using internal version as PluginVersion{major=5, minor=9, bugfix=2} for loading MythicMobs.
[17:32:09] [Server thread/INFO]: [MythicMobs] Hooked into MythicMobs
[17:32:09] [Server thread/INFO]: [Shopkeepers] Hooked into Shopkeepers
[17:32:09] [Server thread/INFO]: [SuperiorSkyblock2] Hooked into SuperiorSkyblock2
[17:32:09] [Server thread/INFO]: [BattlePass] Successfully loaded the pass type with the id: premium
[17:32:09] [Server thread/INFO]: [BattlePass] Successfully loaded the pass type with the id: free
[17:32:09] [Server thread/INFO]: [BattlePass] Register PlaceholderAPI placeholders
[17:32:09] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: battlepass [1.1]
[17:32:09] [Server thread/INFO]: [EpicCraftingsPlus] Enabling EpicCraftingsPlus v7.33.1
[17:32:10] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: epiccraftings [7.33.1]
[17:32:10] [Server thread/INFO]: [EpicCraftings+] Has been enabled! Version: 7.33.1
[17:32:10] [Server thread/INFO]: [EpicCraftings+] Thanks for using my plugin!  ~Ajneb97
[17:32:10] [Server thread/INFO]: There is a new version available. (7.33.2)
[17:32:10] [Server thread/INFO]: You can download it at: https://www.spigotmc.org/resources/39967/
[17:32:10] [Server thread/INFO]: [MythicAnnouncer] Enabling MythicAnnouncer v1.5.0
[17:32:10] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mythicannouncer [1.5.0]
[17:32:10] [Server thread/INFO]: [PhoBan] Enabling PhoBan v1.0.0
[17:32:10] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: phoban [1.0.0]
[17:32:10] [Server thread/INFO]: [PhoBan] [ACF] Enabled Asynchronous Tab Completion Support!
[17:32:10] [Server thread/INFO]: [MythicDungeons] Enabling MythicDungeons v2.0.1-SNAPSHOT
[17:32:10] [Server thread/INFO]: [MythicDungeons] Using PaperListener...
[17:32:10] [Server thread/INFO]: [MythicDungeons] Using default parties! Enabled party support.
[17:32:10] [Server thread/INFO]: [MythicDungeons] MythicMobs plugin found! Enabled MythicMobs support.
[17:32:10] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: md [2.0.1]
[17:32:10] [Server thread/INFO]: [MythicDungeons] * Loaded 38 functions.
[17:32:10] [Server thread/INFO]: [MythicDungeons] * Loaded 14 triggers.
[17:32:10] [Server thread/INFO]: [MythicDungeons] * Loaded 9 conditions.
[17:32:10] [Server thread/INFO]: [MythicDungeons] GUI menus initialized!
[17:32:10] [Server thread/INFO]: [MythicDungeons] Mythic Dungeons v2.0.1 initialized! Happy dungeon-ing!
[17:32:10] [Server thread/INFO]: [ExecutableEvents] Enabling ExecutableEvents v*********
[17:32:10] [Server thread/INFO]: ========*======== ExecutableEvents ========*========
[17:32:11] [Server thread/INFO]: ExecutableEvents Amount of Executable Events configurations loaded: 1
[17:32:11] [Server thread/INFO]: ========*======== ExecutableEvents ========*========
[17:32:11] [Server thread/INFO]: [AuraSkills] Enabling AuraSkills v2.3.3
[17:32:11] [Server thread/INFO]: [AuraSkills] Loaded 21 message files
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook DecentHolograms
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook LuckPerms
[17:32:11] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: auraskills [2.3.3]
[17:32:11] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: aureliumskills [2.3.3]
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook PlaceholderAPI
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook ProtocolLib
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook Vault
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook WorldGuard
[17:32:11] [Server thread/INFO]: [AuraSkills] Successfully registered hook MythicMobs
[17:32:11] [Server thread/INFO]: [AuraSkills] Loaded 158 config options in 41 ms
[17:32:11] [Server thread/INFO]: [AuraSkills] Loaded 3 blocked/disabled worlds
[17:32:12] [Server thread/INFO]: [AuraSkills] [ACF] Enabled Asynchronous Tab Completion Support!
[17:32:12] [Server thread/INFO]: [PhoBanPro] Enabling PhoBanPro v1.2.11
[17:32:12] [Server thread/INFO]: [PhoBanPro] MythicMobs API Class: io.lumine.mythic.bukkit.BukkitAPIHelper
[17:32:12] [Server thread/INFO]: [PhoBanPro] Recode by hoangkiet
[17:32:12] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: phobanpro [1.0]
[17:32:12] [Server thread/INFO]: [InteractiveChat] Enabling InteractiveChat v*******
[17:32:14] [Server thread/INFO]: [InteractiveChat] No custom ProtocolProvider provided, using default ProtocolLib provider.
[17:32:14] [Server thread/INFO]: [InteractiveChat] Opened Sqlite database successfully
[17:32:14] [Server thread/INFO]: [InteractiveChat] InteractiveChat has hooked into Essentials!
[17:32:14] [Server thread/INFO]: [InteractiveChat] InteractiveChat has hooked into DiscordSRV!
[17:32:14] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechat.hooks.discordsrv.DiscordSRVEvents subscribed (2 methods)
[17:32:14] [Server thread/INFO]: [InteractiveChat] InteractiveChat has hooked into ViaVersion!
[17:32:14] [Server thread/INFO]: [InteractiveChat] InteractiveChat has hooked into LuckPerms!
[17:32:14] [Server thread/INFO]: [InteractiveChat] InteractiveChat has hooked into Floodgate!
[17:32:15] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: interactivechat [*******]
[17:32:15] [Server thread/INFO]: [InteractiveChat] InteractiveChat has been Enabled!
[17:32:15] [Server thread/INFO]: [MythicCrucible] Enabling MythicCrucible v2.2.0-SNAPSHOT
[17:32:15] [Server thread/INFO]: [MythicMobs] §6------------------------------------------------
[17:32:15] [Server thread/INFO]: [MythicMobs] §b+ Loading MythicCrucible for Bukkit
[17:32:15] [Server thread/INFO]: [MythicMobs] §6------------------------------------------------
[17:32:15] [Server thread/INFO]: [MythicMobs] Registering Durability Listener
[17:32:19] [Server thread/INFO]: [MythicMobs] Started up bStats Metrics
[17:32:19] [Server thread/INFO]: [MythicMobs] MythicCrucible Support has been enabled!
[17:32:19] [Server thread/INFO]: [MythicMobs] MythicCrucible WorldEdit support enabled!
[17:32:19] [Server thread/INFO]: [MythicMobs] Attached traits to items.
[17:32:19] [Server thread/INFO]: [MythicMobs] Loaded 242 items.
[17:32:19] [Server thread/INFO]: [MythicMobs] Loaded 22 drop tables.
[17:32:19] [Server thread/INFO]: [MythicMobs] ✓ MythicCrucible  v2.2.0 has been successfully loaded!
[17:32:19] [Server thread/INFO]: [MMDiscordNotifs] Enabling MMDiscordNotifs v1.1
[17:32:19] [Server thread/INFO]: [MMDiscordNotifs] DiscordSRV found! Trying to use DiscordSRV for notifications!
[17:32:19] [Server thread/INFO]: [MMDiscordNotifs] MMDiscordNotifs has been enabled!
[17:32:19] [Server thread/INFO]: [MythicLib] Enabling MythicLib v1.7.1-SNAPSHOT
[17:32:19] [Server thread/INFO]: [MythicLib] Hooked onto TextDisplays (holograms)
[17:32:19] [Server thread/INFO]: [MythicMobs] MMO Plugin Support has been enabled!
[17:32:19] [Server thread/INFO]: [MythicLib] Hooked onto MythicMobs
[17:32:19] [Server thread/INFO]: [MythicLib] Hooked onto ProtocolLib
[17:32:19] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mythiclib [1.7.1-SNAPSHOT]
[17:32:19] [Server thread/INFO]: [MythicLib] Hooked onto PlaceholderAPI
[17:32:19] [Server thread/WARN]: [MythicLib] Found a dependency cycle! Please make sure that the plugins involved load with no errors.
[17:32:19] [Server thread/WARN]: [MythicLib] Plugin dependency cycle: [MMDiscordNotifs, DiscordSRV, Multiverse-Core, SuperiorSkyblock2, zMenu, ItemsAdder, MythicMobs, Multiverse-Core]
[17:32:19] [Server thread/INFO]: [Vulcan] Enabling Vulcan v2.9.5
[17:32:20] [Server thread/INFO]: [Vulcan] Server Version: .1.21.4 detected!
[17:32:20] [Server thread/INFO]: [Vulcan] Floodgate 2.0 found. Enabling hook!
[17:32:20] [Server thread/INFO]: [Vulcan] LibsDisguises found. Enabling hook!
[17:32:20] [Server thread/INFO]: [Vulcan] MythicMobs found. Enabling hook!
[17:32:20] [Server thread/INFO]: [Vulcan] GSit found. Enabling hook!
[17:32:20] [Server thread/INFO]: [Vulcan] BStats enabled!
[17:32:20] [Server thread/INFO]: [Vulcan] Registered MythicMobs listener!
[17:32:20] [Server thread/ERROR]: [Vulcan] Failed to register events for class me.frep.vulcan.spigot.Vulcan_nO because dev/geco/gsit/api/event/PlayerGetUpPlayerSitEvent does not exist.
[17:32:20] [Server thread/INFO]: [Vulcan] Registered GSit Listener!
[17:32:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: Vulcan [2.9.5]
[17:32:20] [Server thread/INFO]: [Vulcan] PlaceholderAPI found. Enabling hook!
[17:32:20] [Server thread/INFO]: [MMOItems] Enabling MMOItems v6.10.1-SNAPSHOT
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto MythicMobs
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto ItemsAdder
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto MMOInventory
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto AuraSkills
[17:32:20] [Server thread/INFO]: [MMOItems] Now using AuraSkills as RPG core plugin
[17:32:20] [Server thread/INFO]: [MMOItems KATANA TEST] Could not load base item data 'ability': Missing ability type
[17:32:20] [Server thread/INFO]: [MMOItems STAFF 09] Could not load base item data 'ability': Missing ability type
[17:32:20] [Server thread/INFO]: [MMOItems STAFF THU] Could not load base item data 'ability': Missing ability type
[17:32:20] [Server thread/INFO]: [MMOItems STAFF TESTSUBTRANS] Could not load base item data 'ability': Missing ability type
[17:32:20] [Server thread/INFO]: [MMOItems Template Modifiers] Loading template modifiers, please wait..
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates] Loading item templates, please wait...
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (THRUSTING_SWORD)] Could not post-load item template 'SKILLED_SWORD': Could not find stat with ID 'REQUIRED_DEXTERITY'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'TEST': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'TESTTTTT': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO1': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO2': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO3': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO4': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (SWORD)] Could not post-load item template 'KIEMTHOMO5': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_1': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_2': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_3': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_4': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_5': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_6': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_7': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_8': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_9': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUP_KHOANG_SAN_10': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE_2': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE_3': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE_4': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE_5': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ATOMIC_PICKAXE_6': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'FLASHING_PICKAXE': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'FLASHING_PICKAXE_2': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'FLASHING_PICKAXE_3': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'FLASHING_PICKAXE_4': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'FLASHING_PICKAXE_5': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'SUPER_PICKAXE': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CRYSTALMORGES': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'MINERALBLESSED1': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'BLESSEDMINERAL2': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'BLESSEDMINERAL3': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'TEST': Could not find stat with ID 'ORE_MULTIPLIER_AMOUNT'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUPKINHNGHIEM1': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUPKINHNGHIEM2': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUPKINHNGHIEM3': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUPKINHNGHIEMMAX': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'SERENITY_PEACE': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'ULTIMATE_PICKAXE': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'TEST_MULTI': Could not find stat with ID 'ORE_MULTIPLIER'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (TOOL)] Could not post-load item template 'CUPTRIAN30M': Could not find stat with ID 'EXP_MINE'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (KATANA)] Could not post-load item template 'MASTER_KATANA': Could not find stat with ID 'REQUIRED_DEXTERITY'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (STAFF)] Could not post-load item template 'BANGTRUONG3': Could not find stat with ID 'BUFF_CONFIG'
[17:32:20] [Server thread/INFO]: [MMOItems Item Templates (CATALYST)] Could not post-load item template 'LUCK_CHARM': Could not find stat with ID 'ADDITIONAL_EXPERIENCE'
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto Vault
[17:32:20] [Server thread/INFO]: [MMOItems] Loading crafting stations, please wait..
[17:32:20] [Server thread/WARN]: [MMOItems] Could not load crafting station 'mythical-forge.yml': Cannot invoke "org.bukkit.configuration.ConfigurationSection.getKeys(boolean)" because the return value of "org.bukkit.configuration.ConfigurationSection.getConfigurationSection(String)" is null
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto MMOInventory
[17:32:20] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: mmoitems [6.10.1-SNAPSHOT]
[17:32:20] [Server thread/INFO]: [MMOItems] Hooked onto PlaceholderAPI
[17:32:20] [Server thread/INFO]: [MMOItems] Loading recipes, please wait...
[17:32:20] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for SPEAR GLITCHED; Shaped recipe containing only AIR, ignored.
[17:32:20] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for CROSSBOW MEDIEVAL_CROSSBOW; Shaped recipe containing only AIR, ignored.
[17:32:20] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:20] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:20] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:20] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:21] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for ENCHANTED_ITEM ENCH_ROTTEN_FLESH; Shapeless recipe containing only AIR, ignored.
[17:32:21] [Server thread/INFO]: [PandeLoot] Enabling PandeLoot vdev
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded MythicMobs support
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded Vault support
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded Citizens support
[17:32:21] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: pandeloot [1.0.0]
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded PAPI support
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded DiscordSRV support
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded MMOItems support
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded 51 flags
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded 1 flag packs
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded 1 containers
[17:32:21] [Server thread/INFO]: [PandeLoot] Loaded 0 predefined drops
[17:32:21] [Server thread/INFO]: [MMOInventory] Enabling MMOInventory v2.0-SNAPSHOT
[17:32:21] [Server thread/INFO]: [MMOInventory] Hooked onto PlaceholderAPI
[17:32:21] [Server thread/INFO]: [BeeMinions] Enabling BeeMinions v5.0.2-BETA
[17:32:21] [Server thread/INFO]: [BeeMinions] Database Manager -> Initialized correctly!
[17:32:21] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: beeminions [1.0.0]
[17:32:21] [Server thread/INFO]: [ItemsAdder] Enabling ItemsAdder v4.0.11
[17:32:21] [Server thread/WARN]: [ItemsAdder] LoneLibs is enabled. This plugin is not needed anymore and it's recommended to remove it (if you are not using it for other plugins).
[17:32:21] [Server thread/INFO]: [ItemsAdder] InteractiveChat detected, disabling `cache_font_images_and_effects_replacements`.
[17:32:21] [Server thread/WARN]: [ItemsAdder] Using default server address: 0.0.0.0. Consider setting it in the `config.yml`
[17:32:21] [Server thread/WARN]: [ItemsAdder] Using default server port: 30001. Consider setting it in the `config.yml`
[17:32:21] [Server thread/INFO]: [ItemsAdder] 
                                                   ItemsAdder 4.0.11
  ___  ___        __        __   __   ___  __      ProtocolLib 5.4.0-SNAPSHOT-742
|  |  |__   |\/| /__`  /\  |  \ |  \ |__  |__)     Leaf 1.21.4-496-5311ae8 (MC: 1.21.4)
|  |  |___  |  | .__/ /--\ |__/ |__/ |___ |  \     Build Date: 2025-06-10_18.20.51
                                                   Java Version: 22.0.1
                                                   OS: Linux 5.15.0-141-generic                                               
[17:32:21] [Server thread/INFO]: [ItemsAdder] Loading GlowingEntities API
[17:32:21] [Server thread/INFO]: [GlowingEntities] [GlowingEntities] Found server version 1.21.4
[17:32:21] [Server thread/INFO]: [GlowingEntities] [GlowingEntities] Loaded transparent mappings.
[17:32:21] [Server thread/INFO]: [ItemsAdder] ViaVersion detected. Do not report glitches when joining using game versions different than the server version.
[17:32:21] [Server thread/INFO]: [ItemsAdder] More info: https://github.com/PluginBugs/Issues-ItemsAdder/issues/3683
[17:32:21] [Server thread/INFO]: [ItemsAdder] Registered Skript API
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Please set 'enable-api: true' in Vulcan anticheat config.yml in order to enable compatibility and run '/vulcan reload'.
[17:32:21] [Server thread/INFO]: [ItemsAdder] Registered Citizens NPC Trait: customentity
[17:32:21] [Server thread/INFO]: [ItemsAdder] [Libs] FFmpeg is available at: /home/<USER>/plugins/ItemsAdder/storage/cache/ffmpeg/linux/ffmpeg
[17:32:21] [Server thread/INFO]: [ItemsAdder] [Pack] Extracting internal contents from .jar
[17:32:21] [Server thread/INFO]: [ItemsAdder] [Pack] Extracted 0 default files. No files changes detected.
[17:32:21] [Server thread/WARN]: [ItemsAdder] Detected legacy ItemsAdder pack (pre 3.3.0). You should migrate at some point.
[17:32:21] [Server thread/WARN]: [ItemsAdder] Guidelines: https://a.devs.beer/ia-new-guidelines-330
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Missing namespace in file: /contents/dragonstone_guardians/configs/dragonstone_guardians_collection.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Missing namespace in file: /data/items_packs/ores_and_more/ores_and_more.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_helmet. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_HELMET' exists in MMOItems plugin. File: /contents/lunaria_pack/config/wlunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_chestplate. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_CHESTPLATE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/wlunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_leggings. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_LEGGINGS' exists in MMOItems plugin. File: /contents/lunaria_pack/config/wlunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_boots. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_BOOTS' exists in MMOItems plugin. File: /contents/lunaria_pack/config/wlunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_helmet. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_HELMET' exists in MMOItems plugin. File: /contents/lunaria_pack/config/blunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_chestplate. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_CHESTPLATE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/blunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_leggings. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_LEGGINGS' exists in MMOItems plugin. File: /contents/lunaria_pack/config/blunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_boots. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_BOOTS' exists in MMOItems plugin. File: /contents/lunaria_pack/config/blunaria_armor_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_sword. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_SWORD' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_trident. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_TRIDENT' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_scythe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_SCYTHE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_bow. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_BOW' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_shield. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_SHIELD' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_pickaxe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_PICKAXE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_axe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_AXE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_shovel. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_SHOVEL' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:blunaria_hoe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'BLUNARIA_HOE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_sword. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_SWORD' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_trident. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_TRIDENT' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_scythe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_SCYTHE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_bow. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_BOW' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_shield. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_SHIELD' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_pickaxe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_PICKAXE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_axe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_AXE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_shovel. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_SHOVEL' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/ERROR]: [ItemsAdder] Error loading MMOItems integration for item lunaria_pack:wlunaria_hoe. Please check if the MMOItem of type 'LUNARIA_PACK' and id 'WLUNARIA_HOE' exists in MMOItems plugin. File: /contents/lunaria_pack/config/lunaria_items_config.yml
[17:32:21] [Server thread/WARN]: [ItemsAdder] [Items] 'lunar_studios:ares_helmet'. Warning: Trying to create a custom legacy armor using an incompatible material. 'PAPER' is not a leather armor material. File: /contents/lunar_studios/configs/ares_set/ls_ares.yml
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] Enabling DeluxeBazaar v9.9
[17:32:22] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: bazaar [2.0]
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Enabled PlaceholderAPI support!
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Enabled HeadDatabase support!
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Enabled MMOItems support!
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Your server is running on 1.21.
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Database successfully loaded! Took 2ms to complete!
[17:32:22] [Server thread/INFO]: [DeluxeBazaar] (INFO) Plugin is enabled! Plugin Version: v9.9
[17:32:22] [Server thread/INFO]: [AxEnvoy] Enabling AxEnvoy v2.0.6
[17:32:22] [Server thread/INFO]: [AxEnvoy] Enabled PlaceholderAPI hook!
[17:32:22] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: axenvoy [2.0.6]
[17:32:22] [Server thread/INFO]: [AxEnvoy] Enabled WorldGuard hook!
[17:32:22] [Server thread/INFO]: [DeluxeMenus] Enabling DeluxeMenus v1.14.0-Release
[17:32:22] [Server thread/INFO]: [DeluxeMenus] Successfully hooked into PlaceholderAPI!
[17:32:22] [Server thread/INFO]: [DeluxeMenus] 19 GUI menus loaded!
[17:32:22] [Server thread/INFO]: [DeluxeMenus] You are running the latest version of DeluxeMenus!
[17:32:22] [Server thread/INFO]: [DeluxeMenus] Successfully hooked into Vault!
[17:32:22] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: deluxemenus [1.14.0-Release]
[17:32:22] [Server thread/INFO]: [SoulSkills] Enabling SoulSkills v1.0
[17:32:22] [Server thread/INFO]: [SoulSkills] SoulSkills plugin đã được kích hoạt!
[17:32:22] [Server thread/INFO]: [zMenu] Enabling zMenu v*******
[17:32:22] [Server thread/INFO]: [zMenu v*******] === ENABLE START ===
[17:32:22] [Server thread/INFO]: [zMenu v*******] Plugin Version V*******
[17:32:22] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: zmenu [*******]
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/patterns/pattern_example.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/inventories/pro_inventory.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/inventories/example_punish.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/inventories/basic_inventory.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/inventories/advanced_inventory.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] plugins/zMenu/website/inventories.yml loaded successfully !
[17:32:22] [Server thread/INFO]: [zMenu v*******] Command /basic_command successfully register.
[17:32:22] [Server thread/INFO]: [zMenu v*******] Command /advanced_command successfully register.
[17:32:22] [Server thread/INFO]: [zMenu v*******] Command /pro_command successfully register.
[17:32:22] [Server thread/INFO]: [zMenu v*******] Command /openbook successfully register.
[17:32:22] [Server thread/INFO]: [zMenu v*******] Command /punish successfully register.
[17:32:22] [Server thread/INFO]: [zMenu v*******] 
[17:32:22] [Server thread/INFO]: [zMenu v*******] You can support zMenu by upgrading your account here: https://minecraft-inventory-builder.com/account-upgrade
[17:32:22] [Server thread/INFO]: [zMenu v*******] zMenu’s site includes an inventory editor (under development), a marketplace (already available) is a forum (under development)
[17:32:22] [Server thread/INFO]: [zMenu v*******] 
[17:32:22] [Server thread/INFO]: [zMenu v*******] Use ComponentMeta
[17:32:22] [Server thread/INFO]: [zMenu v*******] Loading 6 commands
[17:32:22] [Server thread/INFO]: [zMenu v*******] === ENABLE DONE (126ms) ===
[17:32:22] [Server thread/INFO]: [CustomCrops] Enabling CustomCrops v3.6.40
[17:32:23] [Server thread/INFO]: [CustomCrops] ItemsAdder hooked!
[17:32:23] [Server thread/INFO]: [CustomCrops] ItemsAdder hooked!
[17:32:23] [Server thread/INFO]: [CustomCrops] Vault hooked!
[17:32:23] [Server thread/INFO]: [CustomCrops] PlaceholderAPI hooked!
[17:32:23] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: customcrops [3.6]
[17:32:23] [Server thread/INFO]: [CustomCrops] BattlePass hooked!
[17:32:23] [Server thread/INFO]: [CustomCrops] Hooked into customcrops
[17:32:23] [Server thread/INFO]: [CustomCrops] WorldGuard hooked!
[17:32:23] [Server thread/INFO]: [CustomCrops] MythicMobs hooked!
[17:32:23] [Server thread/WARN]: [CustomCrops] en_us.yml not exists, using en.yml as default locale.
[17:32:23] [Server thread/INFO]: [InteractiveChatDiscordSRVAddon] Enabling InteractiveChatDiscordSrvAddon v*******
[17:32:23] [customcrops-worker-4/WARN]: [CustomCrops] Update is available: https://github.com/Xiao-MoMi/Custom-Crops/
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "eclipse-collections-forkjoin-11.0.0-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "bungeecord-chat-1.16-R0.4-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "eclipse-collections-11.0.0-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "icu4j-71.1-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "elsa-3.0.0-M5-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "eclipse-collections-api-11.0.0-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "fastutil-8.5.9-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "mapdb-3.0.8-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "kotlin-stdlib-1.6.21-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "lz4-1.3.0-remapped.jar"
[17:32:23] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded library "fest-reflect-1.4.1-remapped.jar"
[17:32:23] [Server thread/INFO]: [InteractiveChat] Loading languages...
[17:32:24] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechatdiscordsrvaddon.listeners.DiscordReadyEvents subscribed (1 methods)
[17:32:24] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechatdiscordsrvaddon.listeners.LegacyDiscordCommandEvents subscribed (1 methods)
[17:32:24] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechatdiscordsrvaddon.listeners.OutboundToDiscordEvents subscribed (16 methods)
[17:32:24] [Server thread/INFO]: [DiscordSRV] API listener com.loohp.interactivechatdiscordsrvaddon.listeners.InboundToGameEvents subscribed (8 methods)
[17:32:24] [Server thread/INFO]: [ICDiscordSrvAddon] InteractiveChat DiscordSRV Addon has hooked into ItemsAdder!
[17:32:24] [Server thread/INFO]: [ICDiscordSrvAddon] InteractiveChat DiscordSRV Addon has been Enabled!
[17:32:24] [Server thread/INFO]: [ShopGUIPlus] Enabling ShopGUIPlus v1.106.1
[17:32:24] [Server thread/INFO]: [AdvancedChests] Enabling AdvancedChests v43.5
[17:32:24] [Server thread/INFO]:     _    ___ __   __ _    _  _   ___  ___  ___    ___  _  _  ___  ___  _____  ___ 
[17:32:24] [Server thread/INFO]:    /_\  |   \\ \ / //_\  | \| | / __|| __||   \  / __|| || || __|/ __||_   _|/ __|
[17:32:24] [Server thread/INFO]:   / _ \ | |) |\ V // _ \ | .` || (__ | _| | |) || (__ | __ || _| \__ \  | |  \__ \
[17:32:24] [Server thread/INFO]:  /_/ \_\|___/  \_//_/ \_\|_|\_| \___||___||___/  \___||_||_||___||___/  |_|  |___/
[17:32:24] [Server thread/INFO]:                                                                                   
[17:32:24] [Server thread/INFO]: [LitLibs] Enabling LitLibs v1.1.32
[17:32:24] [packetevents-update-check-thread/INFO]: [packetevents] Checking for updates, please wait...
[17:32:24] [DiscordSRV - JDA Callback 0/INFO]: [DiscordSRV] Successfully registered 9 slash commands (0 conflicted) for 1 plugins in 1/1 guilds (0 cancelled)
[17:32:24] [Server thread/INFO]: [LitLibs] Version found: 1.21
[17:32:24] [Server thread/INFO]: [LitLibs] LitLibs enabled version v1.1.32
[17:32:24] [Server thread/INFO]: [FeatheredElection] Enabling FeatheredElection v5.0.4-BETA
[17:32:24] [Server thread/INFO]: [FeatheredElection] Update Checker -> Check for updates...
[17:32:24] [Server thread/INFO]: [FeatheredElection] Update Checker -> There is a new version 5.0.5!
[17:32:24] [Server thread/INFO]: [FeatheredElection] Update Checker -> Donwload: https://www.spigotmc.org/resources/113909 !
[17:32:24] [Server thread/INFO]: [FeatheredElection] Loading plugin....
 _____         _   _                 _    _____ _         _   _            Running 5.0.4-BETA
|   __|___ ___| |_| |_ ___ ___ ___ _| |  |   __| |___ ___| |_|_|___ ___    Thanks for using my plugin ❤!
|   __| -_| .'|  _|   | -_|  _| -_| . |  |   __| | -_|  _|  _| | . |   |
|__|  |___|__,|_| |_|_|___|_| |___|___|  |_____|_|___|___|_| |_|___|_|_|
                    Developed by Leo_S

[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> Loading dependencies...
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> PlaceholderAPI loaded successfully.
[17:32:24] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: featheredelection [1.0.0]
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> ShopGUIPlus loaded successfully.
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> MMOItems loaded successfully.
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> Citizens loaded successfully.
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> MythicMobs loaded successfully.
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> Vault loaded successfully.
[17:32:24] [Server thread/INFO]: [FeatheredElection] Dependency Manager -> Data saved successfully in (%sms)
[17:32:24] [Server thread/INFO]: [FeatheredElection] Importer Manager -> 2 mayors were loaded correctly! (0ms)
[17:32:24] [packetevents-update-check-thread/INFO]: [packetevents] There is an update available for PacketEvents! Your build: (2.8.0) | Latest release: (2.9.3)
[17:32:25] [Server thread/INFO]: [FeatheredElection] Discord Manager -> Not loading correctly: The provided token is invalid!
[17:32:25] [Server thread/INFO]: [FeatheredElection] SQLDatabase Manager -> Initialized correctly!
[17:32:25] [Server thread/INFO]: [FeatheredElection] SQLDatabase Manager -> Tables created!
[17:32:25] [Server thread/INFO]: [FeatheredElection] Election Manager -> Starting election due to an active election.
[17:32:25] [Server thread/INFO]: [FeatheredElection] Election Manager -> VotingEventListener Registered successfully.
[17:32:25] [Server thread/INFO]: [FeatheredElection] Election Manager -> NPCEventListener Registered successfully.
[17:32:25] [Server thread/INFO]: [FeatheredElection] Election Manager -> MMOItemsListener Registered successfully.
[17:32:25] [Server thread/INFO]: [ItemEdit] Enabling ItemEdit v3.7.0
[17:32:25] [Server thread/INFO]: [ItemEdit] Hooking into MiniMessageAPI see https://webui.advntr.dev/
[17:32:25] [Server thread/INFO]: [ItemEdit] Selected Storage Type: YAML
[17:32:25] [Server thread/INFO]: [ItemEdit] Hooking into Vault
[17:32:25] [Server thread/INFO]: [ItemEdit] Hooking into PlaceHolderAPI
[17:32:25] [Server thread/INFO]: [ItemEdit] placeholders:
[17:32:25] [Server thread/INFO]: [ItemEdit]   %itemedit_amount_<{itemid}>_[{slot}]_[{player}]%
[17:32:25] [Server thread/INFO]: [ItemEdit]     shows how many itemid player has on slot
[17:32:25] [Server thread/INFO]: [ItemEdit]     <{itemid}> for item id on serveritem
[17:32:25] [Server thread/INFO]: [ItemEdit]     [{slot}] for the slot where the item should be counted, by default inventory
[17:32:25] [Server thread/INFO]: [ItemEdit]       Values: inventory (include offhand), equip (include offhand), inventoryandequip (include offhand), hand, offhand, head, chest, legs, feet
[17:32:25] [Server thread/INFO]: [ItemEdit]     [{player}] for the player, by default self
[17:32:25] [Server thread/INFO]: [ItemEdit]     example: %itemedit_amount_{my_item_id}_{hand}%
[17:32:25] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: itemedit [1.0]
[17:32:25] [Server thread/INFO]: [ItemEdit] Hooking into ShopGuiPlus
[17:32:25] [Server thread/INFO]: [ShopGUIPlus] Registered item provider 'ServerItem'.
[17:32:25] [Server thread/INFO]: [ItemEdit] Hooking into MythicMobs
[17:32:25] [Server thread/INFO]: [ItemEdit] # Enabled (took 81 ms)
[17:32:25] [Server thread/INFO]: [RoseStacker] Enabling RoseStacker v1.5.33
[17:32:26] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: rosestacker [1.5.33]
[17:32:26] [Server thread/INFO]: [RoseLoot] Enabling RoseLoot v1.3.0
[17:32:26] [Server thread/INFO]: [RoseLoot] Data handler connected using SQLite.
[17:32:26] [Server thread/INFO]: [ExtraStorage] Enabling ExtraStorage v1.0
[17:32:26] [Server thread/INFO]: [ExtraStorage] Using ShopGUIPlus as economy provider.
[17:32:26] [Server thread/WARN]: [ExtraStorage] Sound not found in registry: ENTITY_ITEM_PICKUP. Using default sound.
[17:32:26] [Server thread/INFO]: [ExtraStorage] Established database connection.
[17:32:26] [Server thread/INFO]: [ExtraStorage] Database setup completed!
[17:32:26] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: exstorage [1.0]
[17:32:26] [Server thread/INFO]: [ExtraStorage] Hooked into PlaceholderAPI
[17:32:26] [Server thread/INFO]: [LitMinions] Enabling LitMinions v4.4.6
[17:32:26] [Server thread/INFO]: [LitLibs] New provider: LitMinions is hooked into LitLibs!
[17:32:26] [Server thread/INFO]: [LitMinions] Enabling LitMinions
[17:32:26] [Server thread/INFO]: [LitMinions] Selected hologram hook: FancyHolograms
[17:32:26] [Server thread/INFO]: [LitMinions] Selected island hook: SuperiorSkyblock2
[17:32:26] [Server thread/INFO]: [LitMinions] Found price hook: ShopGUIPlus
[17:32:26] [Server thread/INFO]: [LitMinions] Registered PlaceholderAPI hook!
[17:32:26] [Server thread/INFO]: [LitMinions] Found protection hook: SuperiorSkyblock2
[17:32:26] [Server thread/INFO]: [LitMinions] Found PlaceHolderAPI hook
[17:32:26] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: litminions [1.0.0]
[17:32:26] [Server thread/INFO]: [LitMinions] Found chest hook: AdvancedChests
[17:32:26] [Server thread/INFO]: [LitMinions] Found loot hook: RoseLoot
[17:32:26] [Server thread/INFO]: [LitMinions] Found loot hook: ItemsAdder
[17:32:26] [Server thread/INFO]: [LitMinions] Found other hook: FastAsyncWorldEdit
[17:32:26] [Server thread/INFO]: [LitMinions] Created config and lang files.
[17:32:26] [Server thread/INFO]: [LitMinions] Registered events
[17:32:26] [Server thread/INFO]: [LitMinions] LitMinions enabled v4.4.6
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] Deleting old backups...
[17:32:26] [Server thread/INFO]: [PlaceholderAPI] Placeholder expansion registration initializing...
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: 
------------------------------------------
The DeleteOldBackups task has been started
------------------------------------------
You can check the task status using command
/backuper task status
You can cancel the task using command
/backuper task cancel
------------------------------------------
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] DeleteOldBackups task completed
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] Deleting broken backups...
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: 
------------------------------------------
The DeleteBrokenBackups task has been started
------------------------------------------
You can check the task status using command
/backuper task status
You can cancel the task using command
/backuper task cancel
------------------------------------------
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] DeleteBrokenBackups task completed
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] Initializing auto backup...
[17:32:26] [Folia Async Scheduler Thread #3/INFO]: [Backuper] Auto backup initialization completed
[17:32:26] [Server thread/INFO]: [MMOItems Template Modifiers] Preloading template modifiers, please wait..
[17:32:26] [Server thread/INFO]: [MMOItems Item Templates] Preloading item templates, please wait..
[17:32:26] [Server thread/INFO]: [MMOItems KATANA TEST] Could not load base item data 'ability': Missing ability type
[17:32:26] [Server thread/INFO]: [MMOItems STAFF 09] Could not load base item data 'ability': Missing ability type
[17:32:26] [Server thread/INFO]: [MMOItems STAFF THU] Could not load base item data 'ability': Missing ability type
[17:32:26] [Server thread/INFO]: [MMOItems STAFF TESTSUBTRANS] Could not load base item data 'ability': Missing ability type
[17:32:26] [Server thread/INFO]: [MMOItems Template Modifiers] Loading template modifiers, please wait..
[17:32:26] [Server thread/INFO]: [MMOItems Item Templates] Loading item templates, please wait...
[17:32:26] [Server thread/INFO]: [MMOItems Item Templates (THRUSTING_SWORD)] Could not post-load item template 'SKILLED_SWORD': Could not find stat with ID 'REQUIRED_DEXTERITY'
[17:32:26] [Server thread/INFO]: [MMOItems Item Templates (KATANA)] Could not post-load item template 'MASTER_KATANA': Could not find stat with ID 'REQUIRED_DEXTERITY'
[17:32:26] [Server thread/INFO]: [MMOItems Item Templates (CATALYST)] Could not post-load item template 'LUCK_CHARM': Could not find stat with ID 'ADDITIONAL_EXPERIENCE'
[17:32:26] [Server thread/WARN]: [MMOItems] Could not load crafting station 'mythical-forge.yml': Cannot invoke "org.bukkit.configuration.ConfigurationSection.getKeys(boolean)" because the return value of "org.bukkit.configuration.ConfigurationSection.getConfigurationSection(String)" is null
[17:32:26] [Server thread/INFO]: [MMOItems] MMOItems 6.10.1-SNAPSHOT reloaded.
[17:32:26] [Server thread/INFO]: [MMOItems] - 49 Item Types
[17:32:26] [Server thread/INFO]: [MMOItems] - 9 Item Tiers
[17:32:26] [Server thread/INFO]: [MMOItems] - 12 Item Sets
[17:32:26] [Server thread/INFO]: [MMOItems] - 3 Upgrade Templates
[17:32:26] [Server thread/INFO]: [MMOItems] Successfully reloaded recipes.
[17:32:26] [Server thread/INFO]: [MMOItems] - 29 Recipes
[17:32:26] [Server thread/WARN]: [MMOItems] Could not load crafting station 'mythical-forge.yml': Cannot invoke "org.bukkit.configuration.ConfigurationSection.getKeys(boolean)" because the return value of "org.bukkit.configuration.ConfigurationSection.getConfigurationSection(String)" is null
[17:32:26] [Server thread/INFO]: [MMOItems] Successfully reloaded the crafting stations..
[17:32:26] [Server thread/INFO]: [MMOItems] - 3 Crafting Stations
[17:32:26] [Server thread/INFO]: [MMOItems] - 13 Recipes
[17:32:26] [Server thread/INFO]: [MMOItems] Successfully reloaded 217 skills.
[17:32:27] [Server thread/INFO]: Done preparing level "world" (52.405s)
[17:32:27] [Server thread/INFO]: Running delayed init tasks
[17:32:27] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] 
[17:32:27] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] [Importing models]
[17:32:27] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] Loading cache version: R4.0.8
[17:32:27] [Craft Scheduler Thread - 14 - ViaVersion/INFO]: [ViaVersion] Finished mapping loading, shutting down loader executor.
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: [InventoryRollbackPlus] Checking for updates...
[17:32:27] [Craft Scheduler Thread - 22 - Essentials/INFO]: [Essentials] Fetching version information...
[17:32:27] [Craft Scheduler Thread - 30 - PlayerAuctions/INFO]: [PlayerAuctions] Loading auction items...
[17:32:27] [Craft Scheduler Thread - 17 - PlaytimeRewardsPlus/INFO]: [PlaytimeRewards+] Successfully acquired all player data for LeaderBoard.
[17:32:27] [Craft Scheduler Thread - 36 - DecentHolograms/INFO]: [DecentHolograms] Loading holograms... 
[17:32:27] [Craft Scheduler Thread - 59 - InteractiveChat/INFO]: [InteractiveChat] Loading languages...
[17:32:27] [Craft Scheduler Thread - 36 - DecentHolograms/INFO]: [DecentHolograms] Loaded 4 holograms!
[17:32:27] [Craft Scheduler Thread - 2 - PlayerPoints/INFO]: [RoseGarden] An update for PlayerPoints (v3.3.2) is available! You are running v3.3.0.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing abyssal_knight_spawner_slim.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] Importing abyssal_knight_spawner_wide.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-6/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing abyssal_charge.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] Importing abyssion_wave.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-6/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing 11_em_blood_elemental.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing bee_keeper_staff.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Model contains duplicate bone names group. Naming bone with UUID 6591b4a6-09e1-11bf-a3f7-22e8a09d0597
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing Trevius.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing plasma_staff.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ps_cw_light_sentinel_vfx_animation.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing abyssal_puddle.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ps_cw_light_sentinel_vfx_light_sword.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Eye height is below 0. Entity might suffocate.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ps_cw_spirit_forest_vfx_animation.bbmodel.
[17:32:27] [Server thread/INFO]: [GiftCode24] §eĐã có phiên bản mới của plugin! Phiên bản hiện tại: v2.1.0-Stable, Phiên bản mới: v2.2.2-viStable
[17:32:27] [Server thread/WARN]: [GiftCode24] Task #7 for GiftCode24 v2.1.0-Stable generated an exception
java.lang.UnsupportedOperationException: Use BukkitRunnable#runTaskTimer(Plugin, long, long)
	at org.bukkit.craftbukkit.scheduler.CraftScheduler.runTaskTimer(CraftScheduler.java:628) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at GiftCode24-2.1.0-Stable.jar/quangdev05.giftcode24.GiftCode24$1.run(GiftCode24.java:271) ~[GiftCode24-2.1.0-Stable.jar:?]
	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at org.bukkit.craftbukkit.scheduler.CraftScheduler.mainThreadHeartbeat(CraftScheduler.java:474) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1199) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300) ~[leaf-1.21.4.jar:1.21.4-496-5311ae8]
	at java.base/java.lang.Thread.run(Thread.java:1570) ~[?:?]
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing minion2.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing ps_cw_light_sentinel_vfx_part.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: playerpoints [3.3.0]
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing slim_player_projectile.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ps_cw_spirit_forest_vfx_nature_snare.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Eye height is below 0. Entity might suffocate.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing spider_queen_staff.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing soul_fire_staff.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing thunder_staff.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Model contains duplicate bone names group. Naming bone with UUID e717a2a7-a21f-d73d-6de3-b7e6e197f28f
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing ps_cw_spirit_forest_vfx_part.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing minion1.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing dark_magicring.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing treviuspet.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing skill_dark.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing wide_player_projectile.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing light_magicring.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing water_magicring.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing fire_magicring.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-2/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing wind_magicring.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing earth_magicring.bbmodel.
[17:32:27] [Server thread/INFO]: [com.fastasyncworldedit.bukkit.regions.WorldGuardFeature] Plugin 'WorldGuard' found. Using it now.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [Server thread/INFO]: [com.fastasyncworldedit.bukkit.FaweBukkit] Attempting to use plugin 'WorldGuard'
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ero_vfx_slash2_ag.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing ero_vfx_slash_ag.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing ero_vfx_fed_b.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing ero_vfx_stone_crack.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing ero_vfx_red_zone.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-3/INFO]: [ModelEngine] [A] Importing ero_vfx_fed_a.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-3/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-6/INFO]: [ModelEngine] [A] Importing lostassetsoriginal_frostsamurai.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-6/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-1/INFO]: [ModelEngine] [A] Importing ero_vfx_stone_up.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-1/WARN]: [ModelEngine] [A] --Warning: Missing hitbox.
[17:32:27] [Craft Scheduler Thread - 55 - MythicAnnouncer/INFO]: MythicAnnouncer There is a new update available. Update: https://www.spigotmc.org/resources/mythicannouncer-mythicmobs-addon.110567/updates
[17:32:27] [Craft Scheduler Thread - 71 - ItemsAdder/INFO]: [ItemsAdder] [License] Spigot product licensed to: fakefila16 (1282809)
[17:32:27] [Craft Scheduler Thread - 70 - MMOInventory/INFO]: [MMOInventory] A new build is available: 1.9 (you are running 2.0-SNAPSHOT)
[17:32:27] [Craft Scheduler Thread - 70 - MMOInventory/INFO]: [MMOInventory] Download it here: https://www.spigotmc.org/resources/99445/
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: 
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: ===============================================================================
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: A minor update to InventoryRollbackPlus is available!
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: Download at https://www.spigotmc.org/resources/inventoryrollbackplus-1-8-1-16-x.85811/
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: (current: 1.7.0, latest: 1.7.6)
[17:32:27] [Craft Scheduler Thread - 15 - InventoryRollbackPlus/INFO]: ===============================================================================
[17:32:27] [Craft Scheduler Thread - 52 - Quests/INFO]: [Quests] §2Update to 5.2.5: §bhttps://github.com/PikaMug/Quests
[17:32:27] [Craft Scheduler Thread - 38 - DecentHolograms/INFO]: 
[17:32:27] [Craft Scheduler Thread - 38 - DecentHolograms/INFO]: A newer version of DecentHolograms is available. Download it from: 
[17:32:27] [Craft Scheduler Thread - 38 - DecentHolograms/INFO]: - https://www.spigotmc.org/resources/96927/
[17:32:27] [Craft Scheduler Thread - 38 - DecentHolograms/INFO]: - https://modrinth.com/plugin/decentholograms
[17:32:27] [Server thread/INFO]: [DeluxeCoinflip] Registered economy provider 'coinsengine_crystal' using CoinsEngine plugin.
[17:32:27] [Server thread/INFO]: // WARNING WARNING WARNING WARNING WARNING //
[17:32:27] [Server thread/INFO]: Automatically banning accounts can result in unintended and unexpected outcomes, such as causing innocent players to be banned automatically.
[17:32:27] [Server thread/INFO]: If you want to enable this feature, please check and ensure the following:
[17:32:27] [Server thread/INFO]: 1) Your players are not joining from a limited range of IP addresses belonging to one specific host, such as a DDoS protection service forwarding players to your server.
[17:32:27] [Server thread/INFO]: 2) All of your servers have IP forwarding set up correctly (please consult BungeeCord or Velocity documentation to set up IP forwarding, if you have not done so).
[17:32:27] [Server thread/INFO]: 3) None of your user accounts are being randomly associated when checking /alts or /ipreport to view linked accounts on the same IP.
[17:32:27] [Server thread/INFO]: 4) If you have an offline-mode network or permit non-premium users to join, please add your authentication or lobby server to ignored_iphistory_servers to prevent falsely associated accounts (since anyone can join any account).
[17:32:27] [Server thread/INFO]: // WARNING WARNING WARNING WARNING WARNING //
[17:32:27] [Server thread/INFO]: If you acknowledge and accept this disclaimer, please type litebans accept to enable automatic bans.
[17:32:27] [ForkJoinPool-8-worker-4/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-4/INFO]: [ModelEngine] [A] Importing abyssal_knight.bbmodel.
[17:32:27] [Craft Scheduler Thread - 82 - ItemEdit/INFO]: [ItemEdit] New Update at https://spigotmc.org/resources/40993
[17:32:27] [Craft Scheduler Thread - 112 - RoseLoot/INFO]: [RoseGarden] An update for RoseLoot (v1.3.1) is available! You are running v1.3.0.
[17:32:27] [Craft Scheduler Thread - 111 - RoseStacker/INFO]: [RoseGarden] An update for RoseStacker (v1.5.34) is available! You are running v1.5.33.
[17:32:27] [pool-176-thread-1/INFO]: There weren''t enough people to auto-start an envoy event! Rescheduling!
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-2/INFO]: [ModelEngine] [A] Importing ero_argos_two.bbmodel.
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] 
[17:32:27] [ForkJoinPool-8-worker-5/INFO]: [ModelEngine] [A] Importing ero_argos.bbmodel.
[17:32:27] [Server thread/INFO]: Loaded 1370 recipes
[17:32:27] [Server thread/INFO]: Loaded 1481 advancements
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: AxEnvoy » A new version of the plugin is available! Download it on Modrinth!
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: AxEnvoy » Changelog:
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: 2.2.1:
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Fixed an exception with the packet system.
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: 2.2.0:
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Added 1.21.6/7 support
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Added a top-block-finder setting to envoys
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Fixed envoy auto-starting not working sometimes
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Added a debug mode to diagnose issues more easily
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Fixed envoys sometimes stopping earlier
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: 2.1.1:
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - More fixes to make modelengine compatible. (hopefully the last one)
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: 2.1.0:
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Added 1.21.5 support
[17:32:27] [Craft Scheduler Thread - 74 - AxEnvoy/INFO]: - Added a metrics system that provides us some basic information (can be disabled in the plugins/AxAPI/metrics.yml file)Report any issues @ https://github.com/Artillex-Studios/Issues/!
[17:32:27] [Craft Scheduler Thread - 83 - RoseStacker/INFO]: [RoseStacker] Fetched 135 translation locales.
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Content] Loaded 971 items
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] Used Blocks IDs:
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - REAL 0/188
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - REAL_NOTE 0/750
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - REAL_TRANSPARENT 0/63
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - FIRE 0/14
[17:32:27] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] Used Font Images: 471/6608
[17:32:28] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Content] Loaded 37 categories
[17:32:28] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Content] Contents loaded successfully!
[17:32:28] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Pack] Checking resourcepack url: https://www.dropbox.com/scl/fi/95dukh8iu8u0j0iff8uqj/generated.zip?rlkey=59m5ja0ra4gz270ica8q6rpa0&st=32a1mb32aw=1
[17:32:28] [Server thread/INFO]: [Essentials] Essentials found a compatible payment resolution method: Vault Compatibility Layer (v1.7.3-b131)!
[17:32:28] [Craft Scheduler Thread - 76 - zMenu/INFO]: [zMenu v*******] New update available. Your version: *******, latest version: *******
[17:32:28] [Craft Scheduler Thread - 76 - zMenu/INFO]: [zMenu v*******] Download plugin here: https://groupez.dev/resources/253
[17:32:28] [Craft Scheduler Thread - 77 - InteractiveChat/INFO]: [InteractiveChat] Loaded all 1 languages!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'paradox' crate (plugins/ExcellentCrates/crates/paradox.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Animation is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'test' crate (plugins/ExcellentCrates/crates/test.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> No rewards defined!
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Animation is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'online' crate (plugins/ExcellentCrates/crates/online.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Animation is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'mvp' crate (plugins/ExcellentCrates/crates/mvp.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Animation is invalid!
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Hologram Template is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'key-all' crate (plugins/ExcellentCrates/crates/key-all.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Hologram Template is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'titanium' crate (plugins/ExcellentCrates/crates/titanium.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> Hologram Template is invalid!
[17:32:28] [Server thread/WARN]: [ExcellentCrates] Problems in 'magma' crate (plugins/ExcellentCrates/crates/magma.yml):
[17:32:28] [Server thread/ERROR]: [ExcellentCrates] -> No rewards defined!
[17:32:28] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: pa [1.31.1]
[17:32:28] [Server thread/INFO]: [Skript] Loading variables...
[17:32:28] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Checking for server resource pack...
[17:32:28] [Server thread/INFO]: [Skript] Loaded 50 variables in 0.0 seconds
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] 
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] Resource pack zipped.
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A] Generator Profiled:
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A]  - Import Phase: 640.3ms
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A]  - Assets Phase: 806.6ms
[17:32:28] [Craft Scheduler Thread - 8 - ModelEngine/INFO]: [ModelEngine] [A]  - Zipping Phase: 314.6ms
[17:32:28] [Server thread/INFO]: [Skript] Line 40: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} is not set or {_total} is not a number'
[17:32:28] [Server thread/INFO]:     Line: if {_total} is not set or {_total} is not a number:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 44: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 50000 and 50000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 50000 and 50000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 54: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 100000 and 100000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 100000 and 100000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 65: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 200000 and 200000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 200000 and 200000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 77: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 500000 and 500000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 500000 and 500000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 89: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 1000000 and 1000000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 1000000 and 1000000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:28] [Server thread/INFO]: [Skript] Line 101: (muctieunap.sk)
[17:32:28] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 5000000 and 5000000 is not in {muctieunap::server_claimed::*}'
[17:32:28] [Server thread/INFO]:     Line: if {_total} >= 5000000 and 5000000 is not in {muctieunap::server_claimed::*}:
[17:32:28] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 113: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 7500000 and 7500000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 7500000 and 7500000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 124: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 10000000 and 10000000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 10000000 and 10000000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 135: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 15000000 and 15000000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 15000000 and 15000000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 146: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 20000000 and 20000000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 20000000 and 20000000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 157: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 30000000 and 30000000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 30000000 and 30000000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Line 168: (muctieunap.sk)
[17:32:29] [Server thread/INFO]:     Can't understand this condition: '{_total} >= 50000000 and 50000000 is not in {muctieunap::server_claimed::*}'
[17:32:29] [Server thread/INFO]:     Line: if {_total} >= 50000000 and 50000000 is not in {muctieunap::server_claimed::*}:
[17:32:29] [Server thread/INFO]:  
[17:32:29] [Server thread/INFO]: [Skript] Loaded 5 scripts with a total of 19 structures in 1.04 seconds
[17:32:29] [Server thread/INFO]: [Skript] Finished loading.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using RoseStacker as a spawners provider.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using RoseStacker as a stacked-blocks provider.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using LuckPerms as a permissions provider.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using ShopGUIPlus as a prices provider.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Hooked into SuperVanish for support of vanish status of players.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Hooked into Essentials for support of afk status of players.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using Vault as an economy provider.
[17:32:29] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: superior [2024.4-b445]
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Using PlaceholderAPI for placeholders support.
[17:32:29] [Server thread/INFO]: [SuperiorSkyblock2] Detected PaperSpigot - Using async chunk-loading support with PaperMC.
[17:32:29] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: discordsrv [1.29.0]
[17:32:29] [Server thread/INFO]: [AuraSkills] Loaded 11 skills with 235 total sources
[17:32:29] [Server thread/INFO]: [AuraSkills] Loaded 9 stats and 17 traits
[17:32:29] [Server thread/INFO]: [AuraSkills] Loaded 22 pattern rewards and 0 level rewards
[17:32:29] [Server thread/INFO]: [AuraSkills] Loaded 58 loot entries in 5 pools and 2 tables
[17:32:29] [Server thread/INFO]: [AuraSkills] Loaded 7 menus
[17:32:29] [Server thread/INFO]: [CustomCrops] Registry access has been frozen
[17:32:29] [Server thread/INFO]: [CustomCrops] AuraSkills hooked!
[17:32:29] [Server thread/INFO]: [CustomCrops] MMOItems hooked!
[17:32:30] [Server thread/INFO]: [ShopGUIPlus] Registered spawners support for RoseStacker.
[17:32:30] [Server thread/INFO]: [RoseLoot] Registered 211 loot table conditions.
[17:32:30] [Server thread/INFO]: [RoseLoot] Registered 11 loot table types.
[17:32:30] [Server thread/INFO]: [RoseLoot] Registered 21 loot item types.
[17:32:30] [Server thread/INFO]: [RoseLoot] Loaded 10 loot tables.
[17:32:30] [Server thread/INFO]: [org.hibernate.Version] HHH000412: Hibernate ORM core version 6.6.13.Final
[17:32:30] [Server thread/INFO]: [org.hibernate.cache.internal.RegionFactoryInitiator] HHH000026: Second-level cache disabled
[17:32:30] [Server thread/INFO]: [org.hibernate.engine.jdbc.connections.internal.ConnectionProviderInitiator] HHH000130: Instantiating explicit connection provider: org.hibernate.hikaricp.internal.HikariCPConnectionProvider
[17:32:30] [Server thread/INFO]: [org.hibernate.orm.connections.pooling] HHH10001005: Database info:
	Database JDBC URL [jdbc:h2:/home/<USER>/plugins/LitMinions/database;AUTO_RECONNECT=TRUE;FILE_LOCK=NO]
	Database driver: org.h2.Driver
	Database version: 2.3.232
	Autocommit mode: true
	Isolation level: undefined/unknown
	Minimum pool size: 2
	Maximum pool size: 20
[17:32:31] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Pack] ETag: 1752374606540297d
[17:32:32] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Server resource pack found without verification hash: Downloaded
[17:32:32] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Reloading ResourceManager: Default, d29b0c8f5d46e3e65e518058c25aa2d1a9c9066e
[17:32:32] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Registered ModManager "Optifine" of class "com.loohp.interactivechatdiscordsrvaddon.resources.mods.optifine.OptifineManager"
[17:32:32] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Registered ModManager "Chime" of class "com.loohp.interactivechatdiscordsrvaddon.resources.mods.chime.ChimeManager"
[17:32:32] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Loading "Default" resources...
[17:32:33] [Server thread/INFO]: [org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[17:32:33] [Server thread/INFO]: [LitMinions] Loaded 1 minions in 686ms
[17:32:33] [Server thread/INFO]: [LitMinions] Loading items
[17:32:33] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for SPEAR GLITCHED; Shaped recipe containing only AIR, ignored.
[17:32:33] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for CROSSBOW MEDIEVAL_CROSSBOW; Shaped recipe containing only AIR, ignored.
[17:32:33] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:33] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:33] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:33] [Server thread/INFO]: [MythicLib Ingredient m material steel_ingot 1.0..] Invalid MMOItem MATERIAL STEEL_INGOT: No such MMOItem for Type MATERIAL. 
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell1_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell2_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell3_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell4_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell6_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell7_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell8_1' already registered.
[17:32:33] [Server thread/INFO]: Could not register recipe for crafting book: Recipe of name 'shaped_material_hell5_1' already registered.
[17:32:34] [Server thread/INFO]: [MMOItems Custom Crafting] Cannot register custom recipe '1' for ENCHANTED_ITEM ENCH_ROTTEN_FLESH; Shapeless recipe containing only AIR, ignored.
[17:32:34] [Server thread/INFO]: [SafeNET] Thank you for downloading SafeNET!
[17:32:34] [Craft Scheduler Thread - 78 - InteractiveChatDiscordSrvAddon/INFO]: [ICDiscordSrvAddon] Loading "d29b0c8f5d46e3e65e518058c25aa2d1a9c9066e" resources...
[17:32:34] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] [Pack] URL is valid (external). URL: https://www.dropbox.com/scl/fi/95dukh8iu8u0j0iff8uqj/generated.zip?rlkey=59m5ja0ra4gz270ica8q6rpa0&st=32a1mb32aw=1
[17:32:34] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder] ResourcePack
[17:32:34] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - Path: /container/plugins/ItemsAdder/output/generated.zip
[17:32:34] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - Download: https://www.dropbox.com/scl/fi/95dukh8iu8u0j0iff8uqj/generated.zip?rlkey=59m5ja0ra4gz270ica8q6rpa0&st=32a1mb32aw=1
[17:32:34] [Craft Scheduler Thread - 72 - ItemsAdder/INFO]: [ItemsAdder]  - Size: 9 MB/250 MB (3%)
[17:32:34] [Server thread/INFO]: [Citizens] Loaded 12 NPCs.
[17:32:34] [Server thread/INFO]: [BetterFarming] [Integrations] Successfully integrated PlaceholderAPI for parsing placeholders from 3rd party plugins in chat messages and GUI menus
[17:32:34] [Server thread/INFO]: [BetterFarming] [Integrations] Successfully integrated LuckPerms for offline permission lookups.
[17:32:34] [Server thread/INFO]: [BetterFarming] [Integrations] Integrated Bedrock GUI forms for Bedrock players.
[17:32:34] [Server thread/INFO]: [BetterFarming] Added region provider: WorldGuard Type: SERVER
[17:32:34] [Server thread/INFO]: [BetterFarming] Added region provider: SuperiorSkyblock2 Type: SKYBLOCK
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersỈnRadius{r=100}': Failed to load custom targeter PLAYERSỈNRADIUS
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Skill: SilentAndSharpMockery | File: /home/<USER>/plugins/MythicMobs/skills/Floor7Skill.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic KNOCKUP
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: knockup
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Skill: abyssal_knight_reset | File: /home/<USER>/plugins/MythicMobs/skills/abyssal_knight_skills.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic GOTOSPAWN
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: goToSpawn{max=32}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop kafkadrop
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Floor7Boss.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop FloridDrop 1 0.025
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop MisterimDrop 1 0.025
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop Astella 1 0.025
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop DolcyllisDrop 1 0.025
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoDuaHau 1 1
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoTraXanh 1 1
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoSua 1 1
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoBacHa 1 1
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoChanh 1 1
[17:32:34] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:34] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:34] [Server thread/WARN]: [ViaVersion] There is a newer plugin version available: 5.4.1, you're on: 5.3.2-SNAPSHOT
[17:32:34] [Server thread/INFO]: [AdvancedChests] There is a NEW VERSION of AdvancedChests please download it at: https://www.spigotmc.org/resources/79061
[17:32:34] [Server thread/INFO]: [CoreTools] Hooked onto Vault
[17:32:34] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: img [1.0.1]
[17:32:34] [Server thread/INFO]: [PlaceholderAPI] Successfully registered internal expansion: iaplayerstat [1.0.1]
[17:32:34] [Server thread/INFO]: [ItemsAdder] Reloading ItemsAdder Custom Entities Citizens NPCs...
[17:32:34] [Server thread/INFO]: [ItemsAdder] Reloaded 0 ItemsAdder Custom Entities Citizens NPCs.
[17:32:34] [Server thread/INFO]: [Mythic] Reloading plugin...
[17:32:34] [Server thread/INFO]: [MythicMobs] Saving plugin data...
[17:32:34] [Server thread/INFO]: [MythicMobs] ✓Saving Finished
[17:32:34] [Server thread/INFO]: [MythicMobs] Loading Packs...
[17:32:34] [Server thread/INFO]: [MythicMobs] Loading Items...
[17:32:34] [Server thread/INFO]: [MythicMobs] Loading Item Groups...
[17:32:34] [Server thread/INFO]: [MythicMobs] Loading Skills...
[17:32:34] [Server thread/ERROR]: [MythicMobs] Error loading LineConfig: Unbalanced Braces
[17:32:34] [Server thread/ERROR]: [MythicMobs] [Line]: a=1;r=250;spacing=6}]
[17:32:35] [Server thread/INFO]: [MythicMobs] Loading Drop Tables...
[17:32:35] [Server thread/INFO]: [MythicMobs] Loading Random Spawns...
[17:32:35] [Server thread/INFO]: [MythicMobs] Loading Spawn Blocks...
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: Luna_E_Temp | File: /home/<USER>/plugins/MythicMobs/skills/World_Boss_Skills.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill Luna_E
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=Luna_E}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'MIR{r=100}': The 'type' attribute must be a valid MythicMob or MythicEntity type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: Sonetto | File: /home/<USER>/plugins/MythicMobs/mobs/Summer_Dungeon_Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxTimerBegin
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxTimerBegin}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: hoathechilinh | File: /home/<USER>/plugins/MythicMobs/mobs/hoathechilinh.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill aura_honghainhi
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=aura_honghainhi}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxTimerBegin
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxTimerBegin}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxFirstSkill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxFirstSkill}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxSecondSkill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxSecondSkill}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic metaskill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mob: Lux | File: /home/<USER>/plugins/MythicMobs/mobs/World_Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Could not find MetaSkill LuxThirdSkill
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: skill{s=LuxThirdSkill}
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 272 mobs.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 4 vanilla mob overrides.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 0 mob stacks.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 1315 skills.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 47 random spawns.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 242 mythic items.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 22 drop tables.
[17:32:35] [Server thread/INFO]: [MythicMobs] ✓ Loaded 33 mob spawners.
[17:32:35] [Server thread/INFO]: [MythicMobs] Attached traits to items.
[17:32:35] [Server thread/INFO]: [MythicMobs] Loaded 22 drop tables.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Item KNIGHT_BANNER
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/packs/Custom Mobs/Items/Items.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to generate cached menu item
[17:32:35] [Server thread/WARN]: java.lang.IllegalArgumentException
[17:32:35] [Server thread/WARN]: 	at com.google.common.base.Preconditions.checkArgument(Preconditions.java:129)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftRegistry.bukkitToMinecraftHolder(CraftRegistry.java:109)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.block.banner.CraftPatternType.bukkitToMinecraftHolder(CraftPatternType.java:27)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftMetaBanner.applyToItem(CraftMetaBanner.java:77)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftItemStack.setItemMeta(CraftItemStack.java:470)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftItemStack.setItemMeta(CraftItemStack.java:442)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.paper.adapters.item.ItemComponentPaperItemStack.editMeta(ItemComponentPaperItemStack.java:49)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.paper.adapters.item.ItemComponentPaperItemStack.editMeta(ItemComponentPaperItemStack.java:27)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.adapters.BukkitItemStack.applyBannerComponent(BukkitItemStack.java:239)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.api.adapters.items.components.AbstractItemBannerComponent.apply(AbstractItemBannerComponent.java:26)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.generateItemStack(MythicItem.java:825)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.generateItemStack(MythicItem.java:769)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.buildItemCache(MythicItem.java:606)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.lambda$loadItem$0(MythicItem.java:557)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.clock.MythicClock.lambda$runPostReload$1(MythicClock.java:144)
[17:32:35] [Server thread/WARN]: 	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.clock.MythicClock.runPostReload(MythicClock.java:141)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.commands.ReloadCommand.doReloadPart2(ReloadCommand.java:165)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.commands.ReloadCommand.onCommand(ReloadCommand.java:69)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.utils.commands.Command.onCommand(Command.java:64)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.utils.commands.Command.onCommand(Command.java:55)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.command.PluginCommand.execute(PluginCommand.java:45)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.command.SimpleCommandMap.dispatch(SimpleCommandMap.java:169)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.dispatchCommand(CraftServer.java:1036)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.Bukkit.dispatchCommand(Bukkit.java:1114)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//dev.lone.itemsadder.Core.OtherPlugins.MythicMobs.MythicMobsHook.reload(SourceFile:47)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.cl.a(SourceFile:479)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:70)
[17:32:35] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:71)
[17:32:35] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:629)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.ahq.a(SourceFile:75)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.a.e(SourceFile:981)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.afo.f(SourceFile:144)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.a.a(SourceFile:979)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftScheduler.mainThreadHeartbeat(CraftScheduler.java:474)
[17:32:35] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1199)
[17:32:35] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300)
[17:32:35] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1570)
[17:32:35] [Server thread/WARN]: [MythicMobs] [BlockType] 'RED_NETHER_BRICK' is not a valid block type.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ItemFlag 'HIDE_POTION_EFFECTS' does not exist in >=1.20.5.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Item KNIGHT_BANNER
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/packs/Custom Mobs/Items/Items.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to generate cached menu item
[17:32:35] [Server thread/WARN]: java.lang.IllegalArgumentException
[17:32:35] [Server thread/WARN]: 	at com.google.common.base.Preconditions.checkArgument(Preconditions.java:129)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftRegistry.bukkitToMinecraftHolder(CraftRegistry.java:109)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.block.banner.CraftPatternType.bukkitToMinecraftHolder(CraftPatternType.java:27)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftMetaBanner.applyToItem(CraftMetaBanner.java:77)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftItemStack.setItemMeta(CraftItemStack.java:470)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.inventory.CraftItemStack.setItemMeta(CraftItemStack.java:442)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.paper.adapters.item.ItemComponentPaperItemStack.editMeta(ItemComponentPaperItemStack.java:49)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.paper.adapters.item.ItemComponentPaperItemStack.editMeta(ItemComponentPaperItemStack.java:27)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.adapters.BukkitItemStack.applyBannerComponent(BukkitItemStack.java:239)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.api.adapters.items.components.AbstractItemBannerComponent.apply(AbstractItemBannerComponent.java:26)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.generateItemStack(MythicItem.java:825)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.generateItemStack(MythicItem.java:769)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.buildItemCache(MythicItem.java:606)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.core.items.MythicItem.lambda$loadItem$0(MythicItem.java:557)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.clock.MythicClock.lambda$runPostReload$1(MythicClock.java:144)
[17:32:35] [Server thread/WARN]: 	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.clock.MythicClock.runPostReload(MythicClock.java:141)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.commands.ReloadCommand.doReloadPart2(ReloadCommand.java:165)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.commands.ReloadCommand.onCommand(ReloadCommand.java:69)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.utils.commands.Command.onCommand(Command.java:64)
[17:32:35] [Server thread/WARN]: 	at MythicMobsPremium-5.9.2-SNAPSHOT.jar//io.lumine.mythic.bukkit.utils.commands.Command.onCommand(Command.java:55)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.command.PluginCommand.execute(PluginCommand.java:45)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.command.SimpleCommandMap.dispatch(SimpleCommandMap.java:169)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.CraftServer.dispatchCommand(CraftServer.java:1036)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.Bukkit.dispatchCommand(Bukkit.java:1114)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//dev.lone.itemsadder.Core.OtherPlugins.MythicMobs.MythicMobsHook.reload(SourceFile:47)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.cl.a(SourceFile:479)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.plugin.RegisteredListener.callEvent(RegisteredListener.java:70)
[17:32:35] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperEventManager.callEvent(PaperEventManager.java:71)
[17:32:35] [Server thread/WARN]: 	at io.papermc.paper.plugin.manager.PaperPluginManagerImpl.callEvent(PaperPluginManagerImpl.java:131)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.plugin.SimplePluginManager.callEvent(SimplePluginManager.java:629)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.ahq.a(SourceFile:75)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.a.e(SourceFile:981)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.afo.f(SourceFile:144)
[17:32:35] [Server thread/WARN]: 	at ItemsAdder_4.0.11.jar//itemsadder.m.a.a(SourceFile:979)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftTask.run(CraftTask.java:78)
[17:32:35] [Server thread/WARN]: 	at org.bukkit.craftbukkit.scheduler.CraftScheduler.mainThreadHeartbeat(CraftScheduler.java:474)
[17:32:35] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.runServer(MinecraftServer.java:1199)
[17:32:35] [Server thread/WARN]: 	at net.minecraft.server.MinecraftServer.lambda$spin$0(MinecraftServer.java:300)
[17:32:35] [Server thread/WARN]: 	at java.base/java.lang.Thread.run(Thread.java:1570)
[17:32:35] [Server thread/WARN]: [MythicMobs] [BlockType] 'RED_NETHER_BRICK' is not a valid block type.
[17:32:35] [Server thread/INFO]: [Mythic] Mythic has finished reloading!
[17:32:35] [Server thread/INFO]: [MythicMobs] Mythic has finished reloading!
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: statistic [2.0.2]
[17:32:35] [Server thread/INFO]: [PAPI] [Javascript-Expansion] 1 script loaded!
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: javascript [2.1.3]
[17:32:35] [Server thread/WARN]: [PlaceholderAPI] Cannot load expansion coinsapi due to an unknown issue.
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: changeoutput [1.2.2]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: vault [1.8.3]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: luckperms [5.4-R2]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: server [2.6.2]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: tinify [2]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: player [2.0.8]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: localtime [1.2]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: permission [1.0.1]
[17:32:35] [Server thread/INFO]: [PlaceholderAPI] Successfully registered external expansion: essentials [1.5.2]
[17:32:35] [Server thread/INFO]: 11 placeholder hook(s) registered! 1 placeholder hook(s) have an update available.
[17:32:35] [Server thread/INFO]: Done (85.629s)! For help, type "help"
[17:32:35] [Server thread/WARN]: Can't keep up! Is the server overloaded? Running 8947ms or 178 ticks behind
[17:32:35] [Craft Scheduler Thread - 111 - Vault/INFO]: [Vault] Checking for Updates ... 
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersỈnRadius{r=100}': Failed to load custom targeter PLAYERSỈNRADIUS
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: SilentAndSharpMockery | File: /home/<USER>/plugins/MythicMobs/skills/Floor7Skill.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic KNOCKUP
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: knockup
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Config Error for Targeter line 'PlayersInTarget{r=100}': Failed to load custom targeter PLAYERSINTARGET
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: abyssal_knight_reset | File: /home/<USER>/plugins/MythicMobs/skills/abyssal_knight_skills.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic GOTOSPAWN
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: goToSpawn{max=32}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Mechanic CustomMechanic:NULL
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Skill: HuyenLocSkill4TiengDaoDong | File: /home/<USER>/plugins/MythicMobs/skills/HuyenLocTruPhuSkills.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom mechanic EFFECT:TOTEMOFUNDYING
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: effect:totemofundying{duration=60;interval=1}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Condition CustomCondition
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: Unknown
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Failed to load custom condition target
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Mechanic Line: target{a=>0}
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop kafkadrop
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Floor7Boss.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop FloridDrop 1 0.025
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop MisterimDrop 1 0.025
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop Astella 1 0.025
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop DolcyllisDrop 1 0.025
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Dungeon_Bosses.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoDuaHau 1 1
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoTraXanh 1 1
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoSua 1 1
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoBacHa 1 1
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/WARN]: [MythicMobs] ✗ Configuration Error in Drop KeoChanh 1 1
[17:32:35] [Server thread/WARN]: [MythicMobs] --| File: /home/<USER>/plugins/MythicMobs/mobs/Event.yml
[17:32:35] [Server thread/WARN]: [MythicMobs] --| Error Message: Drop type not found.
[17:32:35] [Server thread/INFO]: [WildTools] Loading providers started...
[17:32:35] [Server thread/INFO]: [WildTools]  - Using ShopGUIPlus as PricesProvider.
[17:32:35] [Server thread/INFO]: [WildTools]  - Couldn't find any factions providers for tnt banks, using default one.
[17:32:35] [Server thread/INFO]: [WildTools] Using Vault as an economy provider.
[17:32:35] [Server thread/INFO]: [WildTools] Loading providers done (Took 11ms)
[17:32:35] [Server thread/INFO]: [DeluxeCoinflip] Found and using VAULT, CUSTOM_CURRENCY, COINSENGINE_CRYSTAL economy provider(s).
[17:32:35] [Server thread/INFO]: [BeaconPlus] Registering recipe: example_recipe
[17:32:36] [pool-61-thread-1/INFO]: [BeaconPlus] Loaded 589 chunks (0 beacons)
[17:32:36] [Server thread/WARN]: [Shopkeepers] Shopkeeper 247: There is no Citizens NPC with unique id 67dad88a-140b-4a1a-a373-f514d7339485
[17:32:36] [Server thread/WARN]: [Shopkeepers] Shopkeeper 256: There is no Citizens NPC with unique id 4ce28547-14cc-4d01-b3a4-c0e162bb16d7
[17:32:36] [Server thread/WARN]: [Shopkeepers] Shopkeeper 368: There is no Citizens NPC with unique id a861109b-ea21-4656-8624-cd4d3d235776
[17:32:36] [Server thread/WARN]: [Shopkeepers] Found 3 invalid Citizen shopkeepers! Either enable the setting 'delete-invalid-citizen-shopkeepers' inside the config, or use the command '/shopkeepers cleanupCitizenShopkeepers' to automatically delete these shopkeepers and get rid of these warnings.
[17:32:36] [Server thread/INFO]: [Quests] Loaded 4 Quest(s), 7 Action(s), 1 Condition(s) and 803 Phrase(s)
[17:32:36] [Craft Scheduler Thread - 38 - AuraSkills/INFO]: [AuraSkills] New update available! You are on version 2.3.3, latest version is 2.3.5
[17:32:36] [Craft Scheduler Thread - 38 - AuraSkills/INFO]: [AuraSkills] Download it on Modrinth: https://modrinth.com/plugin/uDdZAVls/version/Fuq2cC7G
[17:32:36] [DiscordSRV - JDA Callback 0/INFO]: [DiscordSRV] Successfully registered 9 slash commands (0 conflicted) for 1 plugins in 1/1 guilds (0 cancelled)
[17:32:36] [Craft Scheduler Thread - 8 - InteractiveChat/INFO]: [InteractiveChat] Loaded all 2 languages!
[17:32:36] [Server thread/INFO]: [BetterFarming] [Integrations] Successfully integrated Vault economy into economy system. Name: EssentialsX Economy
[17:32:36] [Server thread/INFO]: [MythicMobs] Loading ProtocolLib listeners...
[17:32:36] [Craft Scheduler Thread - 22 - Essentials/WARN]: [Essentials] There is a new EssentialsX version available for download: 2.21.1.
[17:32:36] [Craft Scheduler Thread - 22 - Essentials/WARN]: [Essentials] Download it here: https://essentialsx.net/downloads.html?branch=stable
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'farming_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'cactus_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'wood_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'deepslate_redstone' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'redstone_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'bee_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [BeeMinions] Minion 'fishing_minion' loaded sucessfully.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] ================================[ ShopGUI+ 1.106.1 ]================================
[17:32:37] [Server thread/INFO]: [ShopGUIPlus]  
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Need help using ShopGUI+? Check out our Wiki or Join our Discord Support server!
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] https://docs.brcdev.net/ & https://discord.brcdev.net/
[17:32:37] [Server thread/INFO]: [ShopGUIPlus]  
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Vault economy registered.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Exp economy registered.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] PlayerPoints economy registered.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Vault economy enabled.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Exp economy enabled.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] PlayerPoints economy enabled.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Using Vault as default economy provider.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Permissions support enabled.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Enabled item provider for HeadDatabase.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Enabled item provider for MMOItems.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Enabled item provider for ItemsAdder.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Enabled item provider for MythicMobs.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Enabled item provider for ServerItem.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Using RoseStacker for spawners support.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded 12 main menu items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'ores' with 29 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'blocks' with 73 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'blocks12' with 58 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'mobdrops' with 31 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'smithingtemplates' with 19 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'misc' with 25 items.
[17:32:37] [Server thread/WARN]: [ShopGUIPlus] Error occurred when loading heads > 12, item not loaded: Invalid base64 skin specified
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'heads' with 173 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'wood' with 120 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'banners' with 73 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'brewing' with 19 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded shop 'farmfood' with 86 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded 11 shops with total of 706 items.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded 0 permission-based price modifiers.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] Loaded 7 sounds.
[17:32:37] [Server thread/INFO]: [ShopGUIPlus]  
[17:32:37] [Server thread/INFO]: [ShopGUIPlus] ====================================================================================
[17:32:37] [Craft Scheduler Thread - 1 - InsaneAnnouncer/INFO]: [InsaneAnnouncer] Translations successfully loaded & applied (35 Translation files found)
[17:32:38] [Craft Scheduler Thread - 86 - AxVaults/INFO]: ◇ Kho đồ ⇨Có phiên bản mới của AxVaults! (hiện tại: 2.9.0 | mới nhất: 2.9.2)
[17:32:39] [Server thread/INFO]: [ICDiscordSrvAddon] Loaded all resources!
[17:32:40] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Starting Pack Generator...
[17:32:40] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Processing ModelEngine Pack...
[17:32:40] [pool-211-thread-1/INFO]: [UltimateKoth] Loaded 0 Holograms(s).
[17:32:40] [pool-212-thread-1/INFO]: [UltimateKoth] Loaded 0 Commands(s).
[17:32:40] [pool-213-thread-1/INFO]: [UltimateKoth] Loaded 1 Stats(s).
[17:32:40] [pool-210-thread-1/INFO]: [UltimateKoth] Loaded 1 Koth(s).
[17:32:40] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generating Custom Blocks...
[17:32:40] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generating Items...
[17:32:41] [ForkJoinPool.commonPool-worker-2/INFO]: [BetterFarming] There is a new version available: 5.10.5
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Assigning Legacy CMD Materials...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generating Legacy Definitions...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generating Sounds...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Linking Custom Blocks...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generating Atlas...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Zipping Pack...
[17:32:41] [Craft Scheduler Thread - 65 - MythicMobs/INFO]: [Generation] Generation Completed in 535ms
