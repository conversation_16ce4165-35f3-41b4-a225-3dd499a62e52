package shyrcs.discordbot.top.commands;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.interaction.GenericInteractionCreateEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.events.GenericEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import net.dv8tion.jda.api.interactions.commands.OptionType;
import net.dv8tion.jda.api.interactions.commands.build.CommandData;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.OptionData;
import org.bukkit.Bukkit;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;
import shyrcs.discordbot.top.models.TopConfig;

import java.awt.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class TopSlashCommand extends ListenerAdapter {
    
    private final SbmagicTopPlugin plugin;
    private final ConfigManager configManager;
    private final PlaceholderManager placeholderManager;
    private final Logger logger;
    
    public TopSlashCommand(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.configManager = plugin.getConfigManager();
        this.placeholderManager = plugin.getPlaceholderManager();
        this.logger = plugin.getLogger();

        logger.info("TopSlashCommand đã được khởi tạo!");
    }

    @Override
    public void onGenericEvent(GenericEvent event) {
        // Chỉ log các event quan trọng để tránh spam
        String eventName = event.getClass().getSimpleName();
        if (eventName.contains("Interaction") || eventName.contains("Command")) {
            logger.info("🔥 Nhận được event quan trọng: " + eventName);
        }
        super.onGenericEvent(event);
    }

    @Override
    public void onGenericInteractionCreate(GenericInteractionCreateEvent event) {
        logger.info("🎯 Nhận được interaction: " + event.getClass().getSimpleName() + " từ " + event.getUser().getName());
        super.onGenericInteractionCreate(event);
    }

    @Override
    public void onMessageReceived(MessageReceivedEvent event) {
        if (event.getAuthor().isBot()) return;

        String message = event.getMessage().getContentDisplay();

        // Command !top <type>
        if (message.startsWith("!top")) {
            if (message.equals("!top")) {
                // Hiển thị hướng dẫn
                StringBuilder help = new StringBuilder();
                help.append("**Message Commands:**\n");
                help.append("• `!top balance` - Top người giàu nhất\n");

                event.getChannel().sendMessage(help.toString()).queue();
                return;
            }

            String[] parts = message.split(" ");
            if (parts.length >= 2) {
                String topType = parts[1];
                logger.info("📝 Nhận được message command: !top " + topType + " từ " + event.getAuthor().getName());

                // Xử lý như slash command
                processTopCommandFromMessage(event, topType);
            } else {
                event.getChannel().sendMessage("❌ Sử dụng: `!top <type>` (ví dụ: `!top balance`)\n" +
                                             "📋 Gõ `!top` để xem hướng dẫn đầy đủ").queue();
            }
        }
    }
    
    public static CommandData getCommandData() {
        List<String> topChoices = new ArrayList<>();

        // Lấy danh sách top từ ConfigManager (sẽ được cập nhật khi plugin khởi động)
        SbmagicTopPlugin instance = SbmagicTopPlugin.getInstance();
        if (instance != null && instance.getConfigManager() != null) {
            Map<String, TopConfig> configs = instance.getConfigManager().getAllTopConfigs();
            topChoices.addAll(configs.keySet());
            instance.getLogger().info("📋 Tìm thấy " + configs.size() + " config tops: " + topChoices);
        }

        // Nếu chưa có config nào, thêm mặc định
        if (topChoices.isEmpty()) {
            topChoices.add("balance");
            if (instance != null) {
                instance.getLogger().warning("⚠️ Không tìm thấy config, sử dụng mặc định: balance");
            }
        }

        OptionData topOption = new OptionData(OptionType.STRING, "type", "Loại top muốn xem", true);
        for (String choice : topChoices) {
            topOption.addChoice(choice, choice);
        }

        CommandData commandData = Commands.slash("top", "Hiển thị bảng xếp hạng")
                .addOptions(topOption);

        if (instance != null) {
            instance.getLogger().info("🔧 Tạo command data: /top với " + topChoices.size() + " options");
        }

        return commandData;
    }
    
    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        logger.info("🚀 NHẬN ĐƯỢC SLASH COMMAND: " + event.getName() + " từ " + event.getUser().getName());

        // Xử lý ping command (test đơn giản)
        if (event.getName().equals("ping")) {
            logger.info("✅ Xử lý ping command");
            event.reply("🏓 Pong! Bot hoạt động bình thường!\n" +
                       "📝 Sử dụng `!top balance` hoặc `/top balance` để xem leaderboard.")
                    .queue();
            return;
        }

        if (!event.getName().equals("top")) {
            logger.info("❌ Không phải lệnh top hoặc ping, bỏ qua: " + event.getName());
            return;
        }

        logger.info("✅ Xác nhận lệnh /top từ " + event.getUser().getName());

        // Defer reply ngay lập tức để tránh timeout
        event.deferReply().queue(
            success -> {
                logger.info("Đã defer reply thành công");

                String topType = event.getOption("type").getAsString();
                logger.info("Loại top được yêu cầu: " + topType);

                // Chạy async để không block Discord thread
                Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                    try {
                        processTopCommand(event, topType);
                    } catch (Exception e) {
                        logger.severe("Lỗi khi xử lý lệnh top: " + e.getMessage());
                        e.printStackTrace();

                        event.getHook().editOriginal("❌ Có lỗi xảy ra khi tạo bảng xếp hạng: " + e.getMessage())
                                .queue();
                    }
                });
            },
            error -> {
                logger.severe("Lỗi khi defer reply: " + error.getMessage());
                error.printStackTrace();
            }
        );
    }
    
    private void processTopCommand(SlashCommandInteractionEvent event, String topType) {
        logger.info("Bắt đầu xử lý top command: " + topType);

        TopConfig topConfig = configManager.getTopConfig(topType);

        if (topConfig == null) {
            logger.warning("Không tìm thấy config cho top: " + topType);
            event.getHook().editOriginal("❌ Không tìm thấy cấu hình cho top: " + topType)
                    .queue();
            return;
        }

        logger.info("Đã tìm thấy config cho top: " + topConfig.getName());

        try {
            // Tạo embed
            EmbedBuilder embed = new EmbedBuilder();
            embed.setTitle(topConfig.getName());
            embed.setColor(new Color(topConfig.getColor()));
            embed.setTimestamp(Instant.now());

            // Set thumbnail nếu có
            if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
                embed.setThumbnail(topConfig.getThumbnail());
            }

            // Set footer
            String footerText = plugin.getConfig().getString("embed.footer", "Skyblock Magic - Leaderboard");
            embed.setFooter(footerText);

            logger.info("Bắt đầu parse placeholders...");

            // Parse và thêm content
            StringBuilder description = new StringBuilder();
            if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
                description.append(topConfig.getDescription()).append("\n\n");
            }

            for (int i = 0; i < topConfig.getContent().size(); i++) {
                String line = topConfig.getContent().get(i);
                logger.info("Đang parse dòng " + (i+1) + ": " + line);

                String parsedLine = placeholderManager.parsePlaceholders(line);
                String discordLine = placeholderManager.convertMinecraftToDiscord(parsedLine);
                description.append(discordLine).append("\n");

                logger.info("Kết quả parse: " + discordLine);
            }

            embed.setDescription(description.toString());

            logger.info("Đã tạo embed thành công, đang gửi...");

            // Gửi embed
            event.getHook().editOriginalEmbeds(embed.build()).queue(
                success -> logger.info("✅ Đã gửi top " + topType + " thành công!"),
                error -> {
                    logger.severe("❌ Lỗi khi gửi embed: " + error.getMessage());
                    error.printStackTrace();
                    event.getHook().editOriginal("❌ Có lỗi xảy ra khi gửi bảng xếp hạng!")
                            .queue();
                }
            );

        } catch (Exception e) {
            logger.severe("❌ Lỗi trong processTopCommand: " + e.getMessage());
            e.printStackTrace();
            event.getHook().editOriginal("❌ Có lỗi xảy ra: " + e.getMessage())
                    .queue();
        }
    }

    private void processTopCommandFromMessage(MessageReceivedEvent event, String topType) {
        logger.info("Bắt đầu xử lý message command: !top " + topType);

        TopConfig topConfig = configManager.getTopConfig(topType);

        if (topConfig == null) {
            logger.warning("Không tìm thấy config cho top: " + topType);
            event.getChannel().sendMessage("❌ Không tìm thấy cấu hình cho top: " + topType).queue();
            return;
        }

        logger.info("Đã tìm thấy config cho top: " + topConfig.getName());

        // Chạy async để không block Discord thread
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                // Tạo embed
                EmbedBuilder embed = new EmbedBuilder();
                embed.setTitle(topConfig.getName());
                embed.setColor(new Color(topConfig.getColor()));
                embed.setTimestamp(Instant.now());

                // Set thumbnail nếu có
                if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
                    embed.setThumbnail(topConfig.getThumbnail());
                }

                // Set footer
                String footerText = plugin.getConfig().getString("embed.footer", "Skyblock Magic - Leaderboard");
                embed.setFooter(footerText);

                logger.info("Bắt đầu parse placeholders...");

                // Parse và thêm content
                StringBuilder description = new StringBuilder();
                if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
                    description.append(topConfig.getDescription()).append("\n\n");
                }

                for (int i = 0; i < topConfig.getContent().size(); i++) {
                    String line = topConfig.getContent().get(i);
                    logger.info("Đang parse dòng " + (i+1) + ": " + line);

                    String parsedLine = placeholderManager.parsePlaceholders(line);
                    String discordLine = placeholderManager.convertMinecraftToDiscord(parsedLine);
                    description.append(discordLine).append("\n");

                    logger.info("Kết quả parse: " + discordLine);
                }

                embed.setDescription(description.toString());

                logger.info("Đã tạo embed thành công, đang gửi...");

                // Gửi embed
                event.getChannel().sendMessageEmbeds(embed.build()).queue(
                    success -> logger.info("✅ Đã gửi top " + topType + " thành công qua message!"),
                    error -> {
                        logger.severe("❌ Lỗi khi gửi embed: " + error.getMessage());
                        error.printStackTrace();
                        event.getChannel().sendMessage("❌ Có lỗi xảy ra khi gửi bảng xếp hạng!")
                                .queue();
                    }
                );

            } catch (Exception e) {
                logger.severe("❌ Lỗi trong processTopCommandFromMessage: " + e.getMessage());
                e.printStackTrace();
                event.getChannel().sendMessage("❌ Có lỗi xảy ra: " + e.getMessage())
                        .queue();
            }
        });
    }
}
