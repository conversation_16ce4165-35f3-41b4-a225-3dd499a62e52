package shyrcs.discordbot.top.commands;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import net.dv8tion.jda.api.interactions.commands.OptionType;
import net.dv8tion.jda.api.interactions.commands.build.CommandData;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.OptionData;
import org.bukkit.Bukkit;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;
import shyrcs.discordbot.top.models.TopConfig;

import java.awt.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class TopSlashCommand extends ListenerAdapter {
    
    private final SbmagicTopPlugin plugin;
    private final ConfigManager configManager;
    private final PlaceholderManager placeholderManager;
    private final Logger logger;
    
    public TopSlashCommand(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.configManager = plugin.getConfigManager();
        this.placeholderManager = plugin.getPlaceholderManager();
        this.logger = plugin.getLogger();
    }
    
    public static CommandData getCommandData() {
        List<String> topChoices = new ArrayList<>();
        
        // Lấy danh sách top từ ConfigManager (sẽ được cập nhật khi plugin khởi động)
        SbmagicTopPlugin instance = SbmagicTopPlugin.getInstance();
        if (instance != null && instance.getConfigManager() != null) {
            Map<String, TopConfig> configs = instance.getConfigManager().getAllTopConfigs();
            topChoices.addAll(configs.keySet());
        }
        
        // Nếu chưa có config nào, thêm mặc định
        if (topChoices.isEmpty()) {
            topChoices.add("balance");
        }
        
        OptionData topOption = new OptionData(OptionType.STRING, "type", "Loại top muốn xem", true);
        for (String choice : topChoices) {
            topOption.addChoice(choice, choice);
        }
        
        return Commands.slash("top", "Hiển thị bảng xếp hạng")
                .addOptions(topOption);
    }
    
    @Override
    public void onSlashCommandInteraction(SlashCommandInteractionEvent event) {
        if (!event.getName().equals("top")) {
            return;
        }
        
        // Defer reply để có thời gian xử lý
        event.deferReply().queue();
        
        String topType = event.getOption("type").getAsString();
        
        // Chạy async để không block Discord thread
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                processTopCommand(event, topType);
            } catch (Exception e) {
                logger.severe("Lỗi khi xử lý lệnh top: " + e.getMessage());
                e.printStackTrace();
                
                event.getHook().editOriginal("❌ Có lỗi xảy ra khi tạo bảng xếp hạng!")
                        .queue();
            }
        });
    }
    
    private void processTopCommand(SlashCommandInteractionEvent event, String topType) {
        TopConfig topConfig = configManager.getTopConfig(topType);
        
        if (topConfig == null) {
            event.getHook().editOriginal("❌ Không tìm thấy cấu hình cho top: " + topType)
                    .queue();
            return;
        }
        
        // Tạo embed
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle(topConfig.getName());
        embed.setDescription(topConfig.getDescription());
        embed.setColor(new Color(topConfig.getColor()));
        embed.setTimestamp(Instant.now());
        
        // Set thumbnail nếu có
        if (topConfig.getThumbnail() != null && !topConfig.getThumbnail().isEmpty()) {
            embed.setThumbnail(topConfig.getThumbnail());
        }
        
        // Set footer
        String footerText = plugin.getConfig().getString("embed.footer", "SoulMC - Leaderboard");
        embed.setFooter(footerText);
        
        // Parse và thêm content
        StringBuilder description = new StringBuilder();
        if (topConfig.getDescription() != null && !topConfig.getDescription().isEmpty()) {
            description.append(topConfig.getDescription()).append("\n\n");
        }
        
        for (String line : topConfig.getContent()) {
            String parsedLine = placeholderManager.parsePlaceholders(line);
            String discordLine = placeholderManager.convertMinecraftToDiscord(parsedLine);
            description.append(discordLine).append("\n");
        }
        
        embed.setDescription(description.toString());
        
        // Gửi embed
        event.getHook().editOriginalEmbeds(embed.build()).queue(
            success -> logger.info("Đã gửi top " + topType + " thành công!"),
            error -> {
                logger.severe("Lỗi khi gửi embed: " + error.getMessage());
                event.getHook().editOriginal("❌ Có lỗi xảy ra khi gửi bảng xếp hạng!")
                        .queue();
            }
        );
    }
}
