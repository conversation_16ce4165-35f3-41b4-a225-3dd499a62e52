# Placeholder Approach Change

## Thay đổi từ Custom Approach sang MMOItems Standard

### Tr<PERSON><PERSON><PERSON> đ<PERSON> (Custom Approach)
- Sử dụng syntax `%ore_multiplier_rate%` và `%ore_multiplier%`
- Tự tạo placeholder processing system
- Real-time lore processing với events
- Phức tạp và có thể conflict với MMOItems

### Bây giờ (Runtime Processing)
- Sử dụng syntax `{ore_multiplier_rate}` và `{ore_multiplier}`
- Runtime processing khi player tương tác với item
- Sử dụng event listeners để detect interaction
- Tương thích với deprecated API nhưng stable

## Lý do thay đổi

### 1. Tương thích tốt hơn
- Không phụ thuộc vào GenerateLoreEvent (đã deprecated)
- Không conflict với hệ thống placeholder của MMOItems
- Tự động được xử lý khi player tương tác với item

### 2. Performance tốt hơn
- Chỉ xử lý items có placeholder và ore multiplier stats
- Lazy processing - chỉ xử lý khi cần thiết
- Ít overhead hơn

### 3. Maintainability
- Code đơn giản hơn
- Ít dependencies
- Dễ debug và maintain

### 4. Deprecated API
- Tránh sử dụng deprecated methods như getLore() và setLore()
- Sử dụng modern MMOItems API

## Files bị xóa
- `OreMultiplierPlaceholder.java`
- `OreMultiplierPlaceholderHook.java`
- `OreMultiplierLoreProcessor.java`
- `OreMultiplierLoreListener.java` (do GenerateLoreEvent deprecated)

## Files mới/cập nhật
- `OreMultiplierPlaceholderUtil.java` - Utility class xử lý placeholder
- `OreMultiplierPlaceholderListener.java` - Event listener cho runtime processing
- Cập nhật `OreMultiplierStat.java` - Static lore thay vì dynamic placeholder
- Cập nhật `OreMultiplierAmountStat.java` - Static lore thay vì dynamic placeholder

## Cách sử dụng mới

### Trong MMOItems lore:
```yaml
lore:
- "§7− Người Chơi Có {ore_multiplier_rate}% đào được gấp {ore_multiplier} khoáng sản"
```

### Kết quả:
```
§7− Người Chơi Có 25.0% đào được gấp 3.0 khoáng sản
```

## Migration Guide

### Nếu đã sử dụng version cũ:
1. Thay đổi syntax từ `%placeholder%` sang `{placeholder}`
2. Restart server để load code mới
3. Recreate items hoặc reload MMOItems config

### Ví dụ migration:
```yaml
# Cũ
lore:
- "Rate: %ore_multiplier_rate%%"

# Mới  
lore:
- "Rate: {ore_multiplier_rate}%"
```

## Technical Details

### GenerateLoreEvent Hook
```java
@EventHandler(priority = EventPriority.HIGH)
public void onGenerateLore(GenerateLoreEvent event) {
    // Đăng ký placeholder với LoreBuilder
    event.getBuilder().registerPlaceholder("ore_multiplier_rate", value);
    event.getBuilder().registerPlaceholder("ore_multiplier", value);
    
    // MMOItems tự động xử lý placeholder trong lore
}
```

### LoreBuilder Integration
```java
// Trong whenApplied() của stat
item.getLore().registerPlaceholder("ore_multiplier_rate", String.format("%.1f", data.getValue()));
```

## Benefits

### 1. Cleaner Code
- Ít code hơn 70%
- Không cần complex event handling
- Sử dụng built-in MMOItems functionality

### 2. Better Performance
- Chỉ process khi cần thiết
- Không có real-time overhead
- Cached bởi MMOItems

### 3. Future-proof
- Sử dụng stable API
- Không phụ thuộc vào deprecated methods
- Tương thích với future MMOItems updates

### 4. User Experience
- Placeholder hoạt động ngay khi item được tạo
- Không cần wait time hoặc manual refresh
- Consistent với MMOItems behavior
