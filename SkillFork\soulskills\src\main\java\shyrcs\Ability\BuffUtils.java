package shyrcs.Ability;

import org.bukkit.potion.PotionEffectType;

/**
 * Utility class cho Buff system
 * Tránh duplicate code và cung cấp methods chung
 *
 * @SuppressWarnings("deprecation") cho fallback methods với server cũ
 */
@SuppressWarnings("deprecation")
public class BuffUtils {
    
    /**
     * Lấy PotionEffectType từ tên (thay thế cho getByName deprecated)
     * Sử dụng Registry API cho Minecraft 1.21+ với fallback cho server cũ
     */
    public static PotionEffectType getEffectTypeByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        try {
            // Sử dụng Registry để tránh deprecated warning
            return org.bukkit.Registry.EFFECT.stream()
                    .filter(effect -> effect.getKey().getKey().equalsIgnoreCase(name.trim()))
                    .findFirst()
                    .orElse(null);
        } catch (Exception e) {
            // Fallback cho server cũ hoặc khi Registry không khả dụng
            try {
                return PotionEffectType.getByName(name.toUpperCase().trim());
            } catch (Exception fallbackException) {
                return null;
            }
        }
    }
    
    /**
     * Kiểm tra buff ID có hợp lệ không
     */
    public static boolean isValidBuffId(String buffId) {
        return getEffectTypeByName(buffId) != null;
    }
    
    /**
     * Lấy tên key của effect type (thay thế cho getName deprecated)
     */
    public static String getEffectTypeName(PotionEffectType effectType) {
        if (effectType == null) {
            return "UNKNOWN";
        }
        
        try {
            return effectType.getKey().getKey();
        } catch (Exception e) {
            // Fallback cho server cũ
            try {
                return effectType.getName();
            } catch (Exception fallbackException) {
                return "UNKNOWN";
            }
        }
    }
    
    /**
     * Kiểm tra xem effect có phải là buff tích cực không
     */
    public static boolean isPositiveEffect(PotionEffectType type) {
        if (type == null) {
            return false;
        }
        
        // Danh sách các effect tích cực (21 buff)
        return type == PotionEffectType.ABSORPTION || type == PotionEffectType.SPEED ||
               type == PotionEffectType.STRENGTH || type == PotionEffectType.REGENERATION ||
               type == PotionEffectType.RESISTANCE || type == PotionEffectType.FIRE_RESISTANCE ||
               type == PotionEffectType.WATER_BREATHING || type == PotionEffectType.INVISIBILITY ||
               type == PotionEffectType.NIGHT_VISION || type == PotionEffectType.HEALTH_BOOST ||
               type == PotionEffectType.SATURATION || type == PotionEffectType.LUCK ||
               type == PotionEffectType.HASTE || type == PotionEffectType.JUMP_BOOST ||
               type == PotionEffectType.INSTANT_HEALTH || type == PotionEffectType.GLOWING ||
               type == PotionEffectType.SLOW_FALLING || type == PotionEffectType.CONDUIT_POWER ||
               type == PotionEffectType.DOLPHINS_GRACE || type == PotionEffectType.HERO_OF_THE_VILLAGE;
    }
    
    /**
     * Chuyển số thành số La Mã
     */
    public static String toRoman(int number) {
        if (number <= 0) return "";
        if (number == 1) return "I";
        if (number == 2) return "II";
        if (number == 3) return "III";
        if (number == 4) return "IV";
        if (number == 5) return "V";
        if (number == 6) return "VI";
        if (number == 7) return "VII";
        if (number == 8) return "VIII";
        if (number == 9) return "IX";
        if (number == 10) return "X";
        return String.valueOf(number);
    }
    
    /**
     * Validate click type (bao gồm shift click)
     */
    public static boolean isValidClickType(String clickType) {
        return "left_click".equalsIgnoreCase(clickType) ||
               "right_click".equalsIgnoreCase(clickType) ||
               "shift_left_click".equalsIgnoreCase(clickType) ||
               "shift_right_click".equalsIgnoreCase(clickType);
    }
    
    /**
     * Format thời gian từ tick sang giây
     */
    public static int ticksToSeconds(int ticks) {
        return ticks / 20;
    }
    
    /**
     * Format thời gian từ giây sang tick
     */
    public static int secondsToTicks(int seconds) {
        return seconds * 20;
    }

    /**
     * Validate level buff (1-256)
     */
    public static boolean isValidLevel(int level) {
        return level >= 1 && level <= 256;
    }

    /**
     * Chuyển level thành amplifier (level - 1)
     */
    public static int levelToAmplifier(int level) {
        return Math.max(0, Math.min(255, level - 1));
    }

    /**
     * Chuyển amplifier thành level (amplifier + 1)
     */
    public static int amplifierToLevel(int amplifier) {
        return amplifier + 1;
    }

    /**
     * Gửi message lên ActionBar cho player
     * Tương thích với cả Spigot và Paper
     */
    public static void sendActionBar(org.bukkit.entity.Player player, String message) {
        try {
            // Thử sử dụng Paper API trước (hiện đại hơn)
            player.sendActionBar(net.kyori.adventure.text.Component.text(message));
        } catch (Exception e) {
            try {
                // Fallback cho Spigot với BungeeCord API
                net.md_5.bungee.api.chat.TextComponent component = new net.md_5.bungee.api.chat.TextComponent();
                component.setText(message);
                player.spigot().sendMessage(net.md_5.bungee.api.ChatMessageType.ACTION_BAR, component);
            } catch (Exception fallbackException) {
                // Nếu cả hai đều fail, gửi message bình thường
                player.sendMessage(message);
            }
        }
    }

    /**
     * Format thời gian cooldown thành string đẹp
     */
    public static String formatCooldownTime(long seconds) {
        if (seconds <= 0) {
            return "§aSẵn sàng!";
        }

        if (seconds >= 60) {
            long minutes = seconds / 60;
            long remainingSeconds = seconds % 60;
            return "§c" + minutes + ":" + String.format("%02d", remainingSeconds);
        } else {
            return "§c" + seconds + "s";
        }
    }
}
