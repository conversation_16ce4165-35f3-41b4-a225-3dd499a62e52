package me.hsgamer.extrastorage.data.user;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;

/**
 * Stub class for ExtraStorage Storage
 */
public class Storage {
    
    private final String name;
    private final Map<Material, Integer> items;
    
    public Storage(String name) {
        this.name = name;
        this.items = new HashMap<>();
    }
    
    public String getName() {
        return name;
    }
    
    public Map<Material, Integer> getItems() {
        return new HashMap<>(items);
    }
    
    public int getAmount(Material material) {
        return items.getOrDefault(material, 0);
    }
    
    public void setAmount(Material material, int amount) {
        if (amount <= 0) {
            items.remove(material);
        } else {
            items.put(material, amount);
        }
    }
    
    public void addAmount(Material material, int amount) {
        int current = getAmount(material);
        setAmount(material, current + amount);
    }
    
    public boolean removeAmount(Material material, int amount) {
        int current = getAmount(material);
        if (current >= amount) {
            setAmount(material, current - amount);
            return true;
        }
        return false;
    }
    
    public ItemStack toItemStack(Material material) {
        int amount = getAmount(material);
        if (amount > 0) {
            return new ItemStack(material, Math.min(amount, material.getMaxStackSize()));
        }
        return null;
    }

    // Methods that work with String material names
    public int getQuantity(String materialName) {
        try {
            Material material = Material.valueOf(materialName.toUpperCase());
            return getAmount(material);
        } catch (IllegalArgumentException e) {
            return 0;
        }
    }

    public boolean subtract(String materialName, int amount) {
        try {
            Material material = Material.valueOf(materialName.toUpperCase());
            return removeAmount(material, amount);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public boolean add(String materialName, int amount) {
        try {
            Material material = Material.valueOf(materialName.toUpperCase());
            addAmount(material, amount);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public boolean canStore(String materialName) {
        return true; // Simplified - always can store
    }

    // Storage status methods
    public String getStatus() {
        return "ACTIVE";
    }

    public int getSpace() {
        return 1000; // Default space
    }

    public int getUsedSpace() {
        return items.values().stream().mapToInt(Integer::intValue).sum();
    }

    public int getFreeSpace() {
        return getSpace() - getUsedSpace();
    }

    public boolean isMaxSpace() {
        return getUsedSpace() >= getSpace();
    }

    // Method to get items as Map<String, ?>
    public Map<String, Integer> getItemsAsStringMap() {
        Map<String, Integer> result = new HashMap<>();
        for (Map.Entry<Material, Integer> entry : items.entrySet()) {
            result.put(entry.getKey().name(), entry.getValue());
        }
        return result;
    }
}
