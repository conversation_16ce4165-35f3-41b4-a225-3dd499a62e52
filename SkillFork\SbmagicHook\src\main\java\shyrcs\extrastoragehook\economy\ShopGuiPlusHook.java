package shyrcs.extrastoragehook.economy;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.function.Consumer;

/**
 * Economy provider cho ShopGUI+
 * T<PERSON><PERSON> thời disabled do thiếu dependencies
 */
public class ShopGuiPlusHook extends EconomyProvider {

    @Override
    public boolean isHooked() {
        return false; // Tạ<PERSON> thời disabled
    }

    @Override
    public String getPrice(Player player, ItemStack item, int amount) {
        return "0.0";
    }

    @Override
    public int getAmount(ItemStack item) {
        return item.getAmount();
    }

    @Override
    public void sellItem(Player player, ItemStack item, int amount, Consumer<Result> callback) {
        callback.accept(new Result(0, 0.0, false));
    }
}