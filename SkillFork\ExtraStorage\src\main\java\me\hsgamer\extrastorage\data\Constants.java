package me.hsgamer.extrastorage.data;

public final class Constants {

    public static final String INVALID = "__INVALID__";
    private static final String PREFIX = "exstorage";
    public static final String STORAGE_UNLIMITED_PERMISSION = PREFIX + ".storage.unlimited";
    public static final String STORAGE_PICKUP_PERMISSION = PREFIX + ".storage.pickup";
    public static final String PLAYER_HELP_PERMISSION = PREFIX + ".command.player.help";
    public static final String PLAYER_OPEN_PERMISSION = PREFIX + ".command.player.open";
    public static final String PLAYER_TOGGLE_PERMISSION = PREFIX + ".command.player.toggle";
    public static final String PLAYER_FILTER_PERMISSION = PREFIX + ".command.player.filter";
    public static final String PLAYER_PARTNER_PERMISSION = PREFIX + ".command.player.partner";
    public static final String PLAYER_SELL_PERMISSION = PREFIX + ".command.player.sell";
    public static final String PLAYER_WITHDRAW_PERMISSION = PREFIX + ".command.player.withdraw";
    public static final String PLAYER_SEND_PERMISSION = PREFIX + ".command.player.send";
    public static final String ADMIN_HELP_PERMISSION = PREFIX + ".command.admin.help";
    public static final String ADMIN_OPEN_PERMISSION = PREFIX + ".command.admin.open";
    public static final String ADMIN_SPACE_PERMISSION = PREFIX + ".command.admin.space";
    public static final String ADMIN_ADD_PERMISSION = PREFIX + ".command.admin.add";
    public static final String ADMIN_SUBTRACT_PERMISSION = PREFIX + ".command.admin.subtract";
    public static final String ADMIN_TAKE_PERMISSION = PREFIX + ".command.admin.take";
    public static final String ADMIN_SET_PERMISSION = PREFIX + ".command.admin.set";
    public static final String ADMIN_RESET_PERMISSION = PREFIX + ".command.admin.reset";
    public static final String ADMIN_WHITELIST_PERMISSION = PREFIX + ".command.admin.whitelist";
    public static final String ADMIN_RELOAD_PERMISSION = PREFIX + ".command.admin.reload";

    private Constants() {
    }

}
