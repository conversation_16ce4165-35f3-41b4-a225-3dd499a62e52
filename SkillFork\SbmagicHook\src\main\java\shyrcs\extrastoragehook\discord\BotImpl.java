package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.entities.Activity;
import net.dv8tion.jda.api.entities.User;
import net.dv8tion.jda.api.requests.GatewayIntent;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Discord Bot implementation
 */
public class BotImpl {
    
    private JDA jda;
    private final String token;
    
    public BotImpl(String token) {
        this.token = token;
        run();
    }
    
    /**
     * Khởi động bot
     */
    public void run() {
        new Thread(() -> {
            try {
                JDABuilder builder = JDABuilder.createDefault(token);
                builder.enableIntents(
                    GatewayIntent.MESSAGE_CONTENT, 
                    GatewayIntent.GUILD_MEMBERS, 
                    GatewayIntent.GUILD_PRESENCES
                );
                
                // Thiết lập activity
                Activity activity = buildActivity(
                    Library.config.getActivityType(), 
                    Library.config.getActivity()
                );
                builder.setActivity(activity);
                
                // Đăng ký listeners
                builder.addEventListeners(new SlashListener());
                builder.addEventListeners(new MessageListener());
                
                this.jda = builder.build();
                this.jda.awaitReady();
                
                SbMagicHook.info("Discord bot đã kết nối thành công!");
                
                // Đăng ký slash commands
                BotSetup.registerSlashCommands(jda);
                
            } catch (Exception e) {
                SbMagicHook.error("Lỗi khi khởi động Discord bot: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }
    
    /**
     * Tắt bot
     */
    public void shutdown() {
        if (jda != null) {
            jda.shutdown();
            SbMagicHook.info("Discord bot đã được tắt!");
        }
    }
    
    /**
     * Lấy JDA instance
     */
    public JDA getJDA() {
        return jda;
    }
    
    /**
     * Gửi tin nhắn riêng tư
     */
    public void sendPrivateMessage(String userId, String message) {
        if (jda == null) return;
        
        try {
            User user = jda.getUserById(userId);
            if (user != null) {
                user.openPrivateChannel().queue(channel -> {
                    channel.sendMessage(message).queue();
                });
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi gửi tin nhắn riêng tư: " + e.getMessage());
        }
    }
    
    /**
     * Tạo Activity từ config
     */
    private Activity buildActivity(String type, String text) {
        switch (type.toUpperCase()) {
            case "LISTENING":
                return Activity.listening(text);
            case "WATCHING":
                return Activity.watching(text);
            case "COMPETING":
                return Activity.competing(text);
            case "PLAYING":
            default:
                return Activity.playing(text);
        }
    }
    
    /**
     * Kiểm tra bot có sẵn sàng không
     */
    public boolean isReady() {
        return jda != null && jda.getStatus() == JDA.Status.CONNECTED;
    }
}
