package shyrcs.discordbot.top.managers;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import shyrcs.discordbot.top.SbmagicTopPlugin;
import shyrcs.discordbot.top.models.TopConfig;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

public class ConfigManager {
    
    private final SbmagicTopPlugin plugin;
    private final Logger logger;
    private final File topConfigsFolder;
    private final Map<String, TopConfig> topConfigs;
    
    public ConfigManager(SbmagicTopPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.topConfigsFolder = new File(plugin.getDataFolder(), "top");
        this.topConfigs = new HashMap<>();
        
        // Tạo thư mục top nếu chưa tồn tại
        if (!topConfigsFolder.exists()) {
            topConfigsFolder.mkdirs();
            createDefaultTopConfigs();
        }
        
        loadTopConfigs();
    }
    
    private void createDefaultTopConfigs() {
        // Tạo config mẫu cho balance top
        createDefaultBalanceTop();
        logger.info("Đã tạo config top mặc định!");
    }
    
    private void createDefaultBalanceTop() {
        File balanceFile = new File(topConfigsFolder, "balance.yml");
        if (!balanceFile.exists()) {
            YamlConfiguration config = new YamlConfiguration();
            
            config.set("name", "💰 Top Balance");
            config.set("description", "Bảng xếp hạng người giàu nhất server");
            config.set("color", 0xFFD700);
            config.set("thumbnail", "");
            
            // Tạo danh sách content với placeholder AjLeaderboards
            config.set("content", List.of(
                "&7⊂&e#1&7⊃ &a%ajlb_lb_vault_eco_balance_1_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_1_alltime_value% &r%img_coins%",
                "&7⊂&e#2&7⊃ &a%ajlb_lb_vault_eco_balance_2_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_2_alltime_value% &r%img_coins%",
                "&7⊂&e#3&7⊃ &a%ajlb_lb_vault_eco_balance_3_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_3_alltime_value% &r%img_coins%",
                "&7⊂&e#4&7⊃ &a%ajlb_lb_vault_eco_balance_4_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_4_alltime_value% &r%img_coins%",
                "&7⊂&e#5&7⊃ &a%ajlb_lb_vault_eco_balance_5_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_5_alltime_value% &r%img_coins%",
                "&7⊂&e#6&7⊃ &a%ajlb_lb_vault_eco_balance_6_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_6_alltime_value% &r%img_coins%",
                "&7⊂&e#7&7⊃ &a%ajlb_lb_vault_eco_balance_7_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_7_alltime_value% &r%img_coins%",
                "&7⊂&e#8&7⊃ &a%ajlb_lb_vault_eco_balance_8_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_8_alltime_value% &r%img_coins%",
                "&7⊂&e#9&7⊃ &a%ajlb_lb_vault_eco_balance_9_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_9_alltime_value% &r%img_coins%",
                "&7⊂&e#10&7⊃ &a%ajlb_lb_vault_eco_balance_10_alltime_name% &8- &e%ajlb_lb_vault_eco_balance_10_alltime_value% &r%img_coins%"
            ));
            
            try {
                config.save(balanceFile);
            } catch (IOException e) {
                logger.severe("Không thể tạo file balance.yml: " + e.getMessage());
            }
        }
    }
    
    public void loadTopConfigs() {
        topConfigs.clear();
        
        File[] files = topConfigsFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files == null) return;
        
        for (File file : files) {
            String topName = file.getName().replace(".yml", "");
            YamlConfiguration config = YamlConfiguration.loadConfiguration(file);
            
            TopConfig topConfig = new TopConfig(
                topName,
                config.getString("name", topName),
                config.getString("description", ""),
                config.getInt("color", 0x00FF00),
                config.getString("thumbnail", ""),
                config.getStringList("content")
            );
            
            topConfigs.put(topName, topConfig);
            logger.info("Đã tải config top: " + topName);
        }
    }
    
    public TopConfig getTopConfig(String topName) {
        return topConfigs.get(topName);
    }
    
    public Map<String, TopConfig> getAllTopConfigs() {
        return new HashMap<>(topConfigs);
    }
    
    public void reloadConfigs() {
        plugin.reloadConfig();
        loadTopConfigs();
        logger.info("Đã reload tất cả config!");
    }
}
