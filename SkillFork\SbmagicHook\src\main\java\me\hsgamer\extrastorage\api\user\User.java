package me.hsgamer.extrastorage.api.user;

import me.hsgamer.extrastorage.data.user.Storage;
import org.bukkit.OfflinePlayer;

import java.util.ArrayList;
import java.util.List;

/**
 * Stub class for ExtraStorage User
 */
public class User {
    
    private final OfflinePlayer player;
    
    public User(OfflinePlayer player) {
        this.player = player;
    }
    
    public OfflinePlayer getPlayer() {
        return player;
    }
    
    public List<Storage> getStorageList() {
        return new ArrayList<>();
    }
    
    public Storage getStorage(String name) {
        return new Storage(name);
    }

    public Storage getStorage() {
        return new Storage("default");
    }
}
