package shyrcs;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;
import shyrcs.Ability.ExpMineStat;
import shyrcs.Ability.ExpMineListener;
import shyrcs.Ability.OreMultiplierStat;
import shyrcs.Ability.OreMultiplierAmountStat;
import shyrcs.Ability.OreMultiplierListener;
import shyrcs.Ability.OreMultiplierPlaceholderListener;
import shyrcs.Ability.BuffListener;
import shyrcs.Ability.BuffPlaceholderListener;
import shyrcs.Ability.BuffCommand;
import shyrcs.Ability.BuffConfigStat;
import shyrcs.Ability.BuffConfigListener;
import shyrcs.Ability.CooldownManager;

public class SoulSkills extends JavaPlugin {
    @Override
    public void onEnable() {
        // Đăng ký custom stat ExpMineStat
        net.Indyuce.mmoitems.MMOItems.plugin.getStats().register(new ExpMineStat());

        // Đăng ký custom stat OreMultiplierStat
        net.Indyuce.mmoitems.MMOItems.plugin.getStats().register(new OreMultiplierStat());

        // Đăng ký custom stat OreMultiplierAmountStat
        net.Indyuce.mmoitems.MMOItems.plugin.getStats().register(new OreMultiplierAmountStat());

        // Đăng ký custom stat BuffConfigStat
        net.Indyuce.mmoitems.MMOItems.plugin.getStats().register(new BuffConfigStat());

        // Đăng ký listener cho exp mine
        Bukkit.getPluginManager().registerEvents(new ExpMineListener(), this);

        // Đăng ký listener cho ore multiplier
        Bukkit.getPluginManager().registerEvents(new OreMultiplierListener(), this);

        // Đăng ký placeholder listener
        Bukkit.getPluginManager().registerEvents(new OreMultiplierPlaceholderListener(), this);

        // Đăng ký buff listeners
        Bukkit.getPluginManager().registerEvents(new BuffListener(this), this);
        Bukkit.getPluginManager().registerEvents(new BuffPlaceholderListener(), this);
        Bukkit.getPluginManager().registerEvents(new BuffConfigListener(this), this);

        // Đăng ký buff commands
        BuffCommand buffCommand = new BuffCommand();
        getCommand("buffs").setExecutor(buffCommand);
        getCommand("mybuffs").setExecutor(buffCommand);
        getCommand("buffhelp").setExecutor(buffCommand);
        getCommand("cooldown").setExecutor(buffCommand);

        getLogger().info("SoulSkills plugin đã được kích hoạt!");
    }

    @Override
    public void onDisable() {
        // Shutdown CooldownManager
        CooldownManager cooldownManager = CooldownManager.getInstance();
        if (cooldownManager != null) {
            cooldownManager.shutdown();
        }

        getLogger().info("SoulSkills plugin đã tắt!");
    }
}