package me.hsgamer.extrastorage.listeners;

import me.hsgamer.extrastorage.ExtraStorage;
import me.hsgamer.extrastorage.api.user.User;
import me.hsgamer.extrastorage.data.user.UserManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerLoginEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.UUID;

public final class PlayerListener
        extends BaseListener {

    private final UserManager manager;

    public PlayerListener(ExtraStorage instance) {
        super(instance);
        this.manager = instance.getUserManager();
    }

    @EventHandler(priority = EventPriority.LOWEST)
    public void onLogin(PlayerLoginEvent event) {
        if (!manager.isLoaded()) {
            event.disallow(PlayerLoginEvent.Result.KICK_OTHER, "Please wait until the server is fully loaded!");
            return;
        }

        // This needs to be called asynchronously to avoid blocking the main thread
        instance.getServer().getScheduler().runTaskAsynchronously(instance, () -> manager.loadUser(event.getPlayer().getUniqueId()));
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();

        // This needs to be called asynchronously to avoid blocking the main thread
        instance.getServer().getScheduler().runTaskAsynchronously(instance, () -> {
            manager.loadUser(uuid);
            User user = manager.getUser(uuid);
            if (user != null) {
                instance.getStorageListener().addUserToCache(user);
            }
        });
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();

        User user = manager.getUser(uuid);
        if (user == null) return;

        instance.getStorageListener().locCache.values().removeIf(u -> u.getUUID().equals(uuid));
        instance.getStorageListener().removeUserFromCache(user);

        // This needs to be called asynchronously to avoid blocking the main thread
        instance.getServer().getScheduler().runTaskAsynchronously(instance, user::save);
    }

}
