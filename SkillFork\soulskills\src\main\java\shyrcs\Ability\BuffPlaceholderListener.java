package shyrcs.Ability;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class BuffPlaceholderListener implements Listener {
    
    @EventHandler
    public void onBuffEvent(BuffEvent event) {
        Player player = event.getPlayer();
        
        // Log buff event cho debugging
        player.sendMessage("§7[Debug] Buff Event: " + event.getBuffType().getKey().getKey() +
                          " Duration: " + event.getDuration() + "s");
    }
    
    /**
     * <PERSON><PERSON>y thời gian còn lại của một buff cụ thể
     */
    public static int getBuffTimeRemaining(Player player, String buffName) {
        PotionEffectType buffType = BuffUtils.getEffectTypeByName(buffName);
        if (buffType == null) {
            return 0;
        }
        
        PotionEffect effect = player.getPotionEffect(buffType);
        if (effect == null) {
            return 0;
        }
        
        return effect.getDuration() / 20; // <PERSON>y<PERSON><PERSON> từ tick sang giây
    }
    
    /**
     * <PERSON><PERSON><PERSON> tra xem player có buff cụ thể không
     */
    public static boolean hasBuff(Player player, String buffName) {
        PotionEffectType buffType = BuffUtils.getEffectTypeByName(buffName);
        if (buffType == null) {
            return false;
        }

        return player.hasPotionEffect(buffType);
    }

    /**
     * Lấy level của buff
     */
    public static int getBuffLevel(Player player, String buffName) {
        PotionEffectType buffType = BuffUtils.getEffectTypeByName(buffName);
        if (buffType == null) {
            return 0;
        }
        
        PotionEffect effect = player.getPotionEffect(buffType);
        if (effect == null) {
            return 0;
        }
        
        return effect.getAmplifier() + 1; // Amplifier bắt đầu từ 0, level bắt đầu từ 1
    }
    
    /**
     * Lấy danh sách tất cả buff hiện tại của player
     */
    public static String getAllBuffs(Player player) {
        StringBuilder buffs = new StringBuilder();
        
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (buffs.length() > 0) {
                buffs.append(", ");
            }
            buffs.append(BuffUtils.getEffectTypeName(effect.getType()))
                 .append(" (")
                 .append(BuffUtils.ticksToSeconds(effect.getDuration()))
                 .append("s)");
        }
        
        return buffs.length() > 0 ? buffs.toString() : "Không có buff";
    }


}
