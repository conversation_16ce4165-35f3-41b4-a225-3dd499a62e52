package shyrcs.extrastoragehook.discord.commands;

import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.bridge.BridgeHook;
import shyrcs.extrastoragehook.executor.DiscordExecutor;
import shyrcs.extrastoragehook.SbMagicHook;

import java.util.Objects;

/**
 * Discord command để tạo mã kết nối
 */
public class CommandConnect extends DiscordExecutor {
    
    private static final char[] CHAR_MAP = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
    private static final int CODE_LENGTH = 6;
    
    public CommandConnect() {
        super("connect", Library.config.getCommand("connect"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    final String code = generateCode();
                    Library.bridge.write(code, authorId);
                    final String reply = buildSuccessReply(code, parseExpiration());
                    
                    event.reply(reply).setEphemeral(true).queue(hook -> {
                        // Tự động xóa mã sau khi hết hạn
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (!Library.bridge.keyExisted(code)) {
                                    return;
                                }
                                Library.bridge.delete(code);
                                hook.editOriginal(reply.concat("\n> **Lưu ý:** Mã kết nối này đã hết hạn! ❌"))
                                    .queue();
                            }
                        }.runTaskLaterAsynchronously(PluginBoot.main, Library.config.getCodeTimeout() * 20L);
                    });
                    
                    SbMagicHook.info("Đã tạo mã kết nối cho Discord user: " + authorId);
                    
                } catch (Exception e) {
                    SbMagicHook.error("Lỗi khi tạo mã kết nối: " + e.getMessage());
                    if (!event.isAcknowledged()) {
                        event.reply(Library.config.getMessage("error")).setEphemeral(true).queue();
                    }
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    final String code = generateCode();
                    Library.bridge.write(code, authorId);
                    String reply = buildSuccessReply(code, parseExpiration());
                    
                    channel.sendMessage(reply).setMessageReference(message)
                        .queue(sentMessage -> {
                            // Tự động xóa mã sau khi hết hạn
                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    if (!Library.bridge.keyExisted(code)) {
                                        return;
                                    }
                                    Library.bridge.delete(code);
                                    sentMessage.editMessage(reply.concat("\n> **Lưu ý:** Mã kết nối này đã hết hạn! ❌"))
                                        .queue();
                                }
                            }.runTaskLaterAsynchronously(PluginBoot.main, Library.config.getCodeTimeout() * 20L);
                        });
                    
                    SbMagicHook.info("Đã tạo mã kết nối cho Discord user: " + authorId);
                    
                } catch (Exception e) {
                    SbMagicHook.error("Lỗi khi tạo mã kết nối: " + e.getMessage());
                    channel.sendMessage(Library.config.getMessage("error"))
                        .setMessageReference(message).queue();
                }
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public String buildSuccessReply(Object... params) {
        String code = (String) params[0];
        String expiration = (String) params[1];
        
        return "🔗 **Mã kết nối của bạn**\n" +
               "```\n" + code + "\n```\n" +
               "📝 Sử dụng lệnh sau trong Minecraft:\n" +
               "`/sbmagichook hook " + code + "`\n\n" +
               "⏰ Mã này sẽ hết hạn sau **" + expiration + "**";
    }
    
    /**
     * Tạo mã kết nối ngẫu nhiên
     */
    private String generateCode() {
        return BridgeHook.generateKey(CODE_LENGTH, CHAR_MAP);
    }
    
    /**
     * Parse thời gian hết hạn
     */
    private String parseExpiration() {
        int seconds = Library.config.getCodeTimeout();
        if (seconds >= 60) {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            if (remainingSeconds == 0) {
                return minutes + " phút";
            } else {
                return minutes + " phút " + remainingSeconds + " giây";
            }
        } else {
            return seconds + " giây";
        }
    }
}
