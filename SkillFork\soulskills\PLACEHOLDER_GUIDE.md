# Ore Multiplier Placeholder Guide

## Tổng quan
Plugin SoulSkills cung cấp 2 placeholder để hiển thị thông tin Ore Multiplier trong lore của MMOItems (sử dụng syntax chuẩn của MMOItems):

- `{ore_multiplier_rate}` - Tỷ lệ % kích hoạt
- `{ore_multiplier}` - Số lần nhân khoáng sản

## Cách sử dụng

### 1. Trong MMOItems Editor
<PERSON>hi tạo hoặc chỉnh sửa item trong MMOItems, bạn có thể sử dụng placeholder trong phần lore:

```yaml
# Ví dụ trong file config hoặc GUI
lore:
- "§7▪ Ore Multiplier Rate: §a{ore_multiplier_rate}%"
- "§7▪ Ore Multiplier Amount: §a{ore_multiplier}x"
- "§7− Người Chơi Có {ore_multiplier_rate}% đào được gấp {ore_multiplier} kho<PERSON>g sản"
```

### 2. <PERSON><PERSON><PERSON> quả hiển thị
Với cấu hình:
- ORE_MULTIPLIER: 25.0
- ORE_MULTIPLIER_AMOUNT: 3.0

Lore sẽ hiển thị:
```
§7▪ Ore Multiplier Rate: §a25.0%
§7▪ Ore Multiplier Amount: §a3.0x
§7− Người Chơi Có 25.0% đào được gấp 3.0 khoáng sản
```

## Tính năng

### 1. Auto-update
- Placeholder tự động cập nhật khi player cầm item
- Không cần restart server hay reload plugin
- Real-time processing

### 2. Fallback Values
- `{ore_multiplier_rate}` → "0" nếu không có stat
- `{ore_multiplier}` → "2" nếu không có stat (default nhân đôi)

### 3. Format
- Số thập phân được format với 1 chữ số sau dấu phẩy
- Ví dụ: 25.0, 3.0, 10.5

## Ví dụ thực tế

### Pickaxe với Ore Multiplier
```yaml
# Tool config trong MMOItems
DIAMOND_PICKAXE_SUPER:
  material: DIAMOND_PICKAXE
  display-name: "§6§lSuper Pickaxe"
  lore:
  - "§7Một cây cuốc đặc biệt với khả năng"
  - "§7nhân khoáng sản khi đào."
  - ""
  - "§7▪ Tỷ lệ kích hoạt: §a{ore_multiplier_rate}%"
  - "§7▪ Số lần nhân: §a{ore_multiplier}x"
  - ""
  - "§7− Người Chơi Có {ore_multiplier_rate}% đào được gấp {ore_multiplier} khoáng sản"
  stats:
    ORE_MULTIPLIER: 30.0
    ORE_MULTIPLIER_AMOUNT: 4.0
```

### Kết quả trong game:
```
§6§lSuper Pickaxe
§7Một cây cuốc đặc biệt với khả năng
§7nhân khoáng sản khi đào.

§7▪ Tỷ lệ kích hoạt: §a30.0%
§7▪ Số lần nhân: §a4.0x

§7− Người Chơi Có 30.0% đào được gấp 4.0 khoáng sản
```

## Troubleshooting

### Placeholder không hoạt động
1. **Kiểm tra syntax**: Đảm bảo viết đúng `{ore_multiplier_rate}` và `{ore_multiplier}` (dùng dấu ngoặc nhọn)
2. **Kiểm tra stats**: Item phải có ít nhất stat `ORE_MULTIPLIER`
3. **Restart server**: Nếu vẫn không hoạt động, thử restart server

### Hiển thị giá trị mặc định
- `0` cho rate → Item không có stat ORE_MULTIPLIER
- `2` cho multiplier → Item không có stat ORE_MULTIPLIER_AMOUNT

### Performance
- Plugin sử dụng caching để tối ưu performance
- Chỉ process khi cần thiết (khi player cầm item)
- Không ảnh hưởng đến server performance

## API cho developers

### Sử dụng trong code
```java
// Sử dụng GenerateLoreEvent để hook vào quá trình tạo lore
@EventHandler
public void onGenerateLore(GenerateLoreEvent event) {
    // Plugin tự động xử lý placeholder {ore_multiplier_rate} và {ore_multiplier}
    // Không cần code thêm
}
```

### Hook vào event
```java
@EventHandler
public void onOreMultiplier(OreMultiplierEvent event) {
    double rate = event.getOreMultiplierValue();
    double multiplier = event.getMultiplierAmount();
    
    // Custom logic với placeholder values
}
```
