package shyrcs.extrastoragehook.extrastorage;

import me.hsgamer.extrastorage.ExtraStorage;
import me.hsgamer.extrastorage.api.StorageAPI;
import me.hsgamer.extrastorage.api.user.User;
import me.hsgamer.extrastorage.data.user.Storage;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.PluginDescriptionFile;
import shyrcs.extrastoragehook.SbMagicHook;

import java.util.Map;
import java.util.UUID;

/**
 * Hook class để tương tác với ExtraStorage APIs
 * Thay thế cho PreventHopper trong HyperHook
 */
public class ExtraStorageHook {
    
    private final Plugin plugin;
    private final ExtraStorage extraStorage;
    private final StorageAPI storageAPI;
    
    public ExtraStorageHook(Plugin plugin) throws Exception {
        PluginDescriptionFile file = plugin.getDescription();
        if (!assertMainClass(file) || !assertName(file)) {
            throw new IllegalStateException("Invalid version of the plugin 'ExtraStorage'!");
        }
        
        this.plugin = plugin;
        this.extraStorage = (ExtraStorage) plugin;
        this.storageAPI = StorageAPI.getInstance();
        
        SbMagicHook.info("Đã kết nối thành công với ExtraStorage v" + plugin.getDescription().getVersion());
    }
    
    /**
     * Lấy plugin ExtraStorage
     */
    public Plugin getPlugin() {
        return this.plugin;
    }
    
    /**
     * Lấy ExtraStorage instance
     */
    public ExtraStorage getExtraStorage() {
        return this.extraStorage;
    }
    
    /**
     * Lấy StorageAPI instance
     */
    public StorageAPI getStorageAPI() {
        return this.storageAPI;
    }
    
    /**
     * Lấy User từ UUID
     */
    public User getUser(UUID uuid) {
        return storageAPI.getUser(Bukkit.getOfflinePlayer(uuid));
    }
    
    /**
     * Lấy User từ OfflinePlayer
     */
    public User getUser(OfflinePlayer player) {
        return storageAPI.getUser(player);
    }
    
    /**
     * Lấy Storage của người chơi
     */
    public Storage getStorage(UUID uuid) {
        User user = getUser(uuid);
        return user != null ? user.getStorage() : null;
    }
    
    /**
     * Lấy Storage của người chơi
     */
    public Storage getStorage(OfflinePlayer player) {
        return getStorage(player.getUniqueId());
    }
    
    /**
     * Kiểm tra xem người chơi có items trong kho không
     */
    public boolean hasItems(UUID uuid) {
        Storage storage = getStorage(uuid);
        if (storage == null) return false;
        
        Map<String, Integer> items = storage.getItemsAsStringMap();
        return items != null && !items.isEmpty();
    }
    
    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, String materialKey) {
        Storage storage = getStorage(uuid);
        if (storage == null) return 0;
        
        return storage.getQuantity(materialKey);
    }
    
    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, Material material) {
        return getItemAmount(uuid, material.name());
    }
    
    /**
     * Lấy số lượng của một item trong kho
     */
    public long getItemAmount(UUID uuid, ItemStack item) {
        String materialKey = item.getType().name();
        return getItemAmount(uuid, materialKey);
    }
    
    /**
     * Thêm item vào kho
     */
    public boolean addItem(UUID uuid, String materialKey, int amount) {
        Storage storage = getStorage(uuid);
        if (storage == null) return false;
        
        try {
            if (storage.canStore(materialKey)) {
                storage.add(materialKey, amount);
                return true;
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi thêm item vào kho: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * Trừ item từ kho
     */
    public boolean removeItem(UUID uuid, String materialKey, int amount) {
        Storage storage = getStorage(uuid);
        if (storage == null) return false;
        
        try {
            long currentAmount = storage.getQuantity(materialKey);
            if (currentAmount >= amount) {
                storage.subtract(materialKey, amount);
                return true;
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi trừ item từ kho: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * Kiểm tra xem storage có đang active không
     */
    public boolean isStorageActive(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null && "ACTIVE".equals(storage.getStatus());
    }
    
    /**
     * Lấy thông tin không gian kho
     */
    public long getStorageSpace(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null ? storage.getSpace() : 0;
    }
    
    /**
     * Lấy không gian đã sử dụng
     */
    public long getUsedSpace(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null ? storage.getUsedSpace() : 0;
    }
    
    /**
     * Lấy không gian còn trống
     */
    public long getFreeSpace(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null ? storage.getFreeSpace() : 0;
    }
    
    /**
     * Kiểm tra xem kho có đầy không
     */
    public boolean isStorageFull(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null && storage.isMaxSpace();
    }
    
    /**
     * Lấy tất cả items trong kho
     */
    public Map<String, ?> getAllItems(UUID uuid) {
        Storage storage = getStorage(uuid);
        return storage != null ? storage.getItemsAsStringMap() : null;
    }
    
    /**
     * Kiểm tra main class của ExtraStorage
     */
    private boolean assertMainClass(PluginDescriptionFile description) {
        return description.getMain().equals("me.hsgamer.extrastorage.ExtraStorage");
    }
    
    /**
     * Kiểm tra tên plugin
     */
    private boolean assertName(PluginDescriptionFile description) {
        return description.getName().equals("ExtraStorage");
    }
}
