# 🚀 COOLDOWN ACTIONBAR UPDATE

## 📋 **Tóm Tắt Thay Đổi**

Đã cập nhật hệ thống cooldown để hiển thị trên ActionBar thay vì chat, giống như MMOItems.

---

## 🔧 **Files Đã Được Tạo/Sửa**

### **1. BuffUtils.java** ✅
**Thêm methods:**
- `sendActionBar()` - Gửi message lên ActionBar (tương thích Paper + Spigot)
- `formatCooldownTime()` - Format thời gian cooldown đẹp

### **2. CooldownManager.java** ✅ **[MỚI]**
**Tính năng:**
- Quản lý cooldown cho từng player/skill riêng biệt
- Hiển thị cooldown liên tục trên ActionBar
- Task chạy mỗi 0.5 giây để update ActionBar
- Support multiple cooldowns cùng lúc

### **3. BuffListener.java** ✅
**Cập nhật:**
- Sử dụng `CooldownManager` thay vì Map cũ
- Hiển thị cooldown trên ActionBar thay vì chat
- Constructor nhận plugin instance

### **4. BuffConfigStat.java** ✅ **[MỚI]**
**MMOItems Custom Stat:**
- Icon: 🐉 Dragon Breath
- Cho phép nhập format buff trong MMOItems GUI
- Validation thông minh
- Lưu vào NBT với key `MMOITEMS_BUFF_CONFIG`

### **5. BuffConfigListener.java** ✅ **[MỚI]**
**Event Handler:**
- Xử lý khi player sử dụng item có BuffConfig stat
- Kiểm tra click type chính xác
- Áp dụng buff và quản lý cooldown
- Sử dụng NBTItem thay vì VolatileMMOItem

### **6. BuffCommand.java** ✅
**Thêm command:**
- `/cooldown` - Xem cooldown hiện tại
- Cập nhật `/buffhelp` với thông tin MMOItems integration

### **7. SoulSkills.java** ✅
**Main Plugin:**
- Đăng ký BuffConfigStat với MMOItems
- Đăng ký BuffConfigListener
- Đăng ký commands mới
- Shutdown CooldownManager khi disable

### **8. plugin.yml** ✅
**Commands mới:**
- `/cooldown` - Xem cooldown hiện tại

---

## 🎯 **Cách Sử Dụng Mới**

### **Phương Pháp 1: MMOItems GUI (Khuyến Khích)**
1. `/mmoitems edit <type> <id>`
2. Click vào stat "Buff Config" (🐉 Dragon Breath icon)
3. Nhập format: `absorption 2 10 left_click 5`
4. Item sẽ có buff khi player click đúng loại

### **Phương Pháp 2: Chat Input (Cũ)**
1. Cầm item có tên chứa "buff"
2. Click để kích hoạt
3. Nhập format trong chat

---

## ✨ **Tính Năng Mới**

### **🎮 ActionBar Cooldown Display**
- Hiển thị cooldown liên tục trên ActionBar
- Format thời gian thông minh: `5s`, `1:30`, etc.
- Multiple cooldowns hiển thị cùng lúc: `Buff: 5s | Skill: 10s`

### **🐉 MMOItems Integration**
- Custom stat "Buff Config" với icon Dragon Breath
- Validation format tự động
- Lore hiển thị thông tin buff
- NBT storage an toàn

### **⚡ Smart Cooldown System**
- Cooldown riêng biệt cho từng skill/buff
- Task tự động dọn dẹp expired cooldowns
- Memory efficient với ConcurrentHashMap

---

## 🔍 **Technical Details**

### **ActionBar API Compatibility**
```java
// Thử Paper API trước
player.sendActionBar(Component.text(message));

// Fallback Spigot BungeeCord API
player.spigot().sendMessage(ChatMessageType.ACTION_BAR, component);

// Fallback cuối cùng
player.sendMessage(message);
```

### **NBT Storage**
```java
// BuffConfig được lưu với key
"MMOITEMS_BUFF_CONFIG" = "absorption 2 10 left_click 5"

// Kiểm tra
NBTItem nbtItem = NBTItem.get(item);
if (nbtItem.hasTag("MMOITEMS_BUFF_CONFIG")) {
    String config = nbtItem.getString("MMOITEMS_BUFF_CONFIG");
}
```

### **Cooldown Keys**
```java
// Format: "buff_" + buffId.toLowerCase()
"buff_absorption" -> cooldown cho absorption buff
"buff_speed" -> cooldown cho speed buff
```

---

## 🎉 **Kết Quả**

✅ **Cooldown hiển thị trên ActionBar** - Không spam chat nữa
✅ **MMOItems integration hoàn hảo** - Tạo buff item dễ dàng  
✅ **Multiple cooldowns support** - Nhiều buff cùng lúc
✅ **User experience tốt** - Thông báo rõ ràng, format đẹp
✅ **Performance optimized** - Task efficient, memory safe
✅ **Backward compatible** - Vẫn support cách cũ

**Hệ thống cooldown giờ đây hoạt động giống hệt MMOItems!** 🚀
