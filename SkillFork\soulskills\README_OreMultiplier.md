# Ore Multiplier Ability

## Mô tả
Ore Multiplier là một ability mới cho phép công cụ có tỷ lệ % nhất định để nhân đôi khoáng sản khi đào. <PERSON><PERSON> kích hoạt, kho<PERSON>g sản được nhân sẽ tự động thêm vào ExtraStorage của người chơi (nếu có) hoặc rơi ra ground.

## Tính năng

### 1. OreMultiplierStat (Tỷ lệ kích hoạt)
- **Tên stat**: `ORE_MULTIPLIER`
- **Loại**: DoubleStat (giá trị số thực)
- **Phạm vi**: 0-100%
- **Áp dụng cho**: Tools (pickaxe, shovel, etc.)
- **Icon**: Diamond Pickaxe
- **Mô tả**: Tỷ lệ % kích hoạt nhân khoáng sản

### 2. OreMultiplierAmountStat (Số lần nhân)
- **Tên stat**: `ORE_MULTIPLIER_AMOUNT`
- **Loại**: DoubleStat (giá trị số thực)
- **Phạm vi**: 1-10x
- **Áp dụng cho**: Tools (pickaxe, shovel, etc.)
- **Icon**: Gold Ingot
- **Mô tả**: Số lần nhân khoáng sản khi kích hoạt (2 = nhân đôi, 3 = nhân ba, etc.)

### 3. Cách hoạt động
1. Khi player đào ore bằng tool có stat `ORE_MULTIPLIER` và `ORE_MULTIPLIER_AMOUNT`
2. Hệ thống sẽ random theo tỷ lệ % đã cấu hình trong `ORE_MULTIPLIER`
3. Nếu kích hoạt thành công:
   - Tính toán số lượng ore được nhân theo `ORE_MULTIPLIER_AMOUNT`
   - Ví dụ: Drop gốc 2x Diamond, multiplier 3x → Thêm 4x Diamond (2 * (3-1))
   - Thử thêm vào ExtraStorage của player
   - Nếu không thể thêm vào storage, drop ra ground
   - Gửi thông báo cho player

### 4. Hook với ExtraStorage
- Tự động detect ExtraStorage plugin
- Kiểm tra storage status và capacity
- Thêm item vào storage nếu có thể
- Fallback drop ra ground nếu không thể thêm vào storage

## Ore được hỗ trợ

| Ore | Drop gốc | Số lượng |
|-----|----------|----------|
| Coal Ore | Coal | 1 |
| Iron Ore | Raw Iron | 1 |
| Copper Ore | Raw Copper | 2-5 (random) |
| Gold Ore | Raw Gold | 1 |
| Redstone Ore | Redstone | 4-5 (random) |
| Lapis Ore | Lapis Lazuli | 4-8 (random) |
| Diamond Ore | Diamond | 1 |
| Emerald Ore | Emerald | 1 |
| Nether Gold Ore | Gold Nugget | 2-5 (random) |
| Nether Quartz Ore | Quartz | 1 |
| Ancient Debris | Ancient Debris | 1 |

*Tất cả các variant Deepslate cũng được hỗ trợ*

## Cách sử dụng

### 1. Tạo MMOItem với Ore Multiplier
1. Mở MMOItems editor
2. Tạo hoặc edit một tool (pickaxe, shovel, etc.)
3. Thêm stat "Ore Multiplier" - nhập tỷ lệ % kích hoạt (0-100)
4. Thêm stat "Ore Multiplier Amount" - nhập số lần nhân (1-10)
5. Save item

### 2. Ví dụ cấu hình
```yaml
# Pickaxe với 25% chance nhân 3 lần ore
ORE_MULTIPLIER: 25.0
ORE_MULTIPLIER_AMOUNT: 3.0
```

### 3. Thông báo trong game
- Khi thêm vào storage: `§a[Ore Multiplier] §7Đã thêm §a3.0x §e4x Diamond §7vào storage!`
- Khi drop ra ground: `§a[Ore Multiplier] §7Đã nhân §a3.0x §e4x Diamond§7!`

### 4. Placeholder Support
Plugin hỗ trợ các placeholder sau để hiển thị trong lore:

- **{ore_multiplier_rate}**: Hiển thị tỷ lệ % kích hoạt
- **{ore_multiplier}**: Hiển thị số lần nhân

#### Cách sử dụng:
1. **Trong MMOItems lore**: Thêm placeholder vào lore của item
2. **Auto-update**: Placeholder tự động được thay thế khi player tương tác với item

#### Ví dụ sử dụng trong lore:
```yaml
lore:
- "§7− Người Chơi Có {ore_multiplier_rate}% đào được gấp {ore_multiplier} khoáng sản"
```

Kết quả hiển thị: `§7− Người Chơi Có 25.0% đào được gấp 3.0 khoáng sản`

#### Khi nào placeholder được cập nhật:
- Khi player cầm item (PlayerItemHeldEvent)
- Khi player click vào item trong inventory
- Khi player mở inventory chứa item

### 5. Lưu ý về cấu hình
- **Chỉ có ORE_MULTIPLIER**: Mặc định nhân đôi (2x)
- **Cả hai stats**: Sử dụng giá trị đã cấu hình
- **Multiplier Amount < 1**: Tự động fallback về 2x
- **Multiplier Amount > 10**: Bị giới hạn để tránh lag
- **Placeholder**: Tự động cập nhật khi player tương tác với item
- **Performance**: Chỉ xử lý items có placeholder và ore multiplier stats

## Events

### OreMultiplierEvent
Custom event được gọi khi ore multiplier kích hoạt:

```java
@EventHandler
public void onOreMultiplier(OreMultiplierEvent event) {
    Player player = event.getPlayer();
    Block block = event.getBlock();
    double multiplierValue = event.getOreMultiplierValue(); // Tỷ lệ % kích hoạt
    ItemStack originalDrop = event.getOriginalDrop();
    int multipliedAmount = event.getMultipliedAmount(); // Số lượng được thêm
    boolean addedToStorage = event.isAddedToStorage();
    double multiplierAmount = event.getMultiplierAmount(); // Số lần nhân (2x, 3x, etc.)

    // Custom logic here
}
```

### GenerateLoreEvent Integration
Plugin cũng hook vào GenerateLoreEvent của MMOItems để xử lý placeholder tự động.

## Yêu cầu
- MMOItems plugin (bắt buộc)
- MythicLib (dependency của MMOItems)
- ExtraStorage plugin (tùy chọn, cho auto storage)

## Cài đặt
1. Đảm bảo MMOItems và MythicLib đã được cài đặt
2. Copy file SoulSkills.jar vào thư mục plugins
3. Restart server để load SoulSkills plugin
4. Stat "Ore Multiplier" sẽ xuất hiện trong MMOItems editor

## Lưu ý
- Chỉ hoạt động với GameMode SURVIVAL và ADVENTURE
- Không hoạt động với Creative/Spectator mode
- Tỷ lệ kích hoạt dựa trên random, không đảm bảo chính xác 100%
- Plugin sử dụng reflection để hook với ExtraStorage, không cần ExtraStorage trong classpath
- Nếu ExtraStorage không có hoặc lỗi, item sẽ drop ra ground
- Tương thích với tất cả phiên bản ExtraStorage có API

## Troubleshooting

### Placeholder không hoạt động
- **Kiểm tra syntax**: Đảm bảo viết đúng `{ore_multiplier_rate}` và `{ore_multiplier}` (dùng dấu ngoặc nhọn)
- **Kiểm tra stats**: Item phải có ít nhất stat `ORE_MULTIPLIER`
- **Restart server**: Nếu vẫn không hoạt động, thử restart server

### ExtraStorage không hoạt động
- **Lỗi API đã sửa**: Plugin đã được cập nhật để tương thích với ExtraStorage API mới nhất
- **Plugin không tìm thấy**: Đảm bảo ExtraStorage plugin đã được cài đặt và load
- **Storage đầy**: Kiểm tra storage capacity và settings

### Ore Multiplier không kích hoạt
- **Kiểm tra tool**: Đảm bảo tool có stat `ORE_MULTIPLIER` > 0
- **Kiểm tra ore**: Chỉ hoạt động với các loại ore được hỗ trợ
- **GameMode**: Chỉ hoạt động với Survival và Adventure mode
- **Tỷ lệ random**: Tỷ lệ kích hoạt dựa trên random, không đảm bảo 100%

### Hiển thị giá trị mặc định
- `0` cho rate → Item không có stat ORE_MULTIPLIER
- `2` cho multiplier → Item không có stat ORE_MULTIPLIER_AMOUNT
