package shyrcs.discordbot.top;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.requests.GatewayIntent;
import shyrcs.discordbot.top.commands.TopSlashCommand;
import shyrcs.discordbot.top.commands.ReloadCommand;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;

import java.util.logging.Logger;

public class SbmagicTopPlugin extends JavaPlugin {

    private static SbmagicTopPlugin instance;
    private JDA jda;
    private ConfigManager configManager;
    private PlaceholderManager placeholderManager;
    private Logger logger;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();

        // L<PERSON>u config mặc định
        saveDefaultConfig();

        // Khởi tạo managers
        configManager = new ConfigManager(this);
        placeholderManager = new PlaceholderManager(this);

        // Đăng ký commands
        getCommand("sbmagictop").setExecutor(new ReloadCommand(this));

        // Khởi tạo Discord bot
        initializeDiscordBot();

        logger.info("SbmagicTop đã được kích hoạt!");
    }

    @Override
    public void onDisable() {
        if (jda != null) {
            jda.shutdown();
        }
        logger.info("SbmagicTop đã được tắt!");
    }

    private void initializeDiscordBot() {
        FileConfiguration config = getConfig();
        String token = config.getString("discord.token");
        String guildId = config.getString("discord.guild-id");

        if (token == null || token.equals("YOUR_BOT_TOKEN_HERE")) {
            logger.severe("Vui lòng cấu hình Discord bot token trong config.yml!");
            return;
        }

        if (guildId == null || guildId.equals("YOUR_GUILD_ID_HERE")) {
            logger.severe("Vui lòng cấu hình Guild ID trong config.yml!");
            return;
        }

        try {
            jda = JDABuilder.createDefault(token)
                    .enableIntents(GatewayIntent.GUILD_MESSAGES)
                    .build();

            jda.awaitReady();

            // Đăng ký slash commands
            jda.getGuildById(guildId).updateCommands()
                    .addCommands(TopSlashCommand.getCommandData())
                    .queue();

            // Đăng ký event listeners
            jda.addEventListener(new TopSlashCommand(this));

            logger.info("Discord bot đã kết nối thành công!");

        } catch (Exception e) {
            logger.severe("Không thể khởi tạo Discord bot: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static SbmagicTopPlugin getInstance() {
        return instance;
    }

    public JDA getJDA() {
        return jda;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public PlaceholderManager getPlaceholderManager() {
        return placeholderManager;
    }
}