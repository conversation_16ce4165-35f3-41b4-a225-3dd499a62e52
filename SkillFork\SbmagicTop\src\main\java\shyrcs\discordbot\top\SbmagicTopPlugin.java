package shyrcs.discordbot.top;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.requests.GatewayIntent;
import net.dv8tion.jda.api.interactions.commands.build.Commands;
import net.dv8tion.jda.api.interactions.commands.build.SlashCommandData;
import shyrcs.discordbot.top.commands.TopSlashCommand;
import shyrcs.discordbot.top.commands.ReloadCommand;
import shyrcs.discordbot.top.managers.ConfigManager;
import shyrcs.discordbot.top.managers.PlaceholderManager;

import java.util.logging.Logger;

public class SbmagicTopPlugin extends JavaPlugin {

    private static SbmagicTopPlugin instance;
    private JDA jda;
    private ConfigManager configManager;
    private PlaceholderManager placeholderManager;
    private Logger logger;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();

        // <PERSON><PERSON><PERSON> config mặc định
        saveDefaultConfig();

        // Khởi tạo managers
        configManager = new ConfigManager(this);
        placeholderManager = new PlaceholderManager(this);

        // Đăng ký commands
        getCommand("sbmagictop").setExecutor(new ReloadCommand(this));

        // Khởi tạo Discord bot
        initializeDiscordBot();

        logger.info("SbmagicTop đã được kích hoạt!");
    }

    @Override
    public void onDisable() {
        if (jda != null) {
            jda.shutdown();
        }
        logger.info("SbmagicTop đã được tắt!");
    }

    private void initializeDiscordBot() {
        FileConfiguration config = getConfig();
        String token = config.getString("discord.token");
        String guildId = config.getString("discord.guild-id");

        if (token == null || token.equals("YOUR_BOT_TOKEN_HERE")) {
            logger.severe("Vui lòng cấu hình Discord bot token trong config.yml!");
            return;
        }

        if (guildId == null || guildId.equals("YOUR_GUILD_ID_HERE")) {
            logger.severe("Vui lòng cấu hình Guild ID trong config.yml!");
            return;
        }

        try {
            logger.info("Đang khởi tạo Discord bot với token: " + token.substring(0, 10) + "...");
            logger.info("Guild ID: " + guildId);

            jda = JDABuilder.createDefault(token)
                    .enableIntents(GatewayIntent.GUILD_MESSAGES, GatewayIntent.MESSAGE_CONTENT)
                    .build();

            logger.info("Đang chờ bot sẵn sàng...");
            jda.awaitReady();
            logger.info("Bot đã sẵn sàng!");

            // Đăng ký event listeners trước
            TopSlashCommand topCommand = new TopSlashCommand(this);
            jda.addEventListener(topCommand);
            logger.info("Đã đăng ký event listener!");

            // Xóa tất cả commands cũ và đăng ký lại
            logger.info("Đang xóa và đăng ký lại slash commands...");

            // Xóa guild commands cũ
            jda.getGuildById(guildId).updateCommands().queue(
                success -> {
                    logger.info("🗑️ Đã xóa guild commands cũ");

                    // Đăng ký guild commands mới (cả top và ping test)
                    SlashCommandData topCommand = TopSlashCommand.getCommandData();
                    SlashCommandData pingCommand = Commands.slash("ping", "Test bot response");

                    jda.getGuildById(guildId).updateCommands()
                            .addCommands(topCommand, pingCommand)
                            .queue(
                                success2 -> logger.info("✅ Đã đăng ký guild slash commands mới!"),
                                error2 -> {
                                    logger.severe("❌ Lỗi khi đăng ký guild slash commands: " + error2.getMessage());
                                    error2.printStackTrace();
                                }
                            );
                },
                error -> {
                    logger.severe("❌ Lỗi khi xóa guild commands: " + error.getMessage());
                    error.printStackTrace();
                }
            );

            // Xóa global commands cũ
            jda.updateCommands().queue(
                success -> {
                    logger.info("🗑️ Đã xóa global commands cũ");

                    // Đăng ký global commands mới (cả top và ping test)
                    SlashCommandData topCommandGlobal = TopSlashCommand.getCommandData();
                    SlashCommandData pingCommandGlobal = Commands.slash("ping", "Test bot response");

                    jda.updateCommands()
                            .addCommands(topCommandGlobal, pingCommandGlobal)
                            .queue(
                                success2 -> logger.info("✅ Đã đăng ký global slash commands mới!"),
                                error2 -> {
                                    logger.severe("❌ Lỗi khi đăng ký global slash commands: " + error2.getMessage());
                                    error2.printStackTrace();
                                }
                            );
                },
                error -> {
                    logger.severe("❌ Lỗi khi xóa global commands: " + error.getMessage());
                    error.printStackTrace();
                }
            );

            logger.info("Discord bot đã kết nối thành công!");
            logger.info("Bot user: " + jda.getSelfUser().getName() + "#" + jda.getSelfUser().getDiscriminator());

        } catch (Exception e) {
            logger.severe("Không thể khởi tạo Discord bot: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static SbmagicTopPlugin getInstance() {
        return instance;
    }

    public JDA getJDA() {
        return jda;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public PlaceholderManager getPlaceholderManager() {
        return placeholderManager;
    }
}