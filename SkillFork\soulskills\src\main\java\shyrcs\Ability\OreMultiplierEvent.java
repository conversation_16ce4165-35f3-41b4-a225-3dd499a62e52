package shyrcs.Ability;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.entity.Player;
import org.bukkit.block.Block;
import org.bukkit.inventory.ItemStack;

public class OreMultiplierEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    private final Player player;
    private final Block block;
    private final double oreMultiplierValue;
    private final ItemStack originalDrop;
    private final int multipliedAmount;
    private final boolean addedToStorage;
    private final double multiplierAmount;

    public OreMultiplierEvent(Player player, Block block, double oreMultiplierValue,
                             ItemStack originalDrop, int multipliedAmount, boolean addedToStorage, double multiplierAmount) {
        this.player = player;
        this.block = block;
        this.oreMultiplierValue = oreMultiplierValue;
        this.originalDrop = originalDrop;
        this.multipliedAmount = multipliedAmount;
        this.addedToStorage = addedToStorage;
        this.multiplierAmount = multiplierAmount;
    }
    
    public Player getPlayer() {
        return player;
    }
    
    public Block getBlock() {
        return block;
    }
    
    public double getOreMultiplierValue() {
        return oreMultiplierValue;
    }
    
    public ItemStack getOriginalDrop() {
        return originalDrop;
    }
    
    public int getMultipliedAmount() {
        return multipliedAmount;
    }
    
    public boolean isAddedToStorage() {
        return addedToStorage;
    }

    public double getMultiplierAmount() {
        return multiplierAmount;
    }

    @Override
    public HandlerList getHandlers() {
        return handlers;
    }

    public static HandlerList getHandlerList() {
        return handlers;
    }
}
