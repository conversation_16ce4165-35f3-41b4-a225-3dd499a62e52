package shyrcs.extrastoragehook.discord.commands;

import me.hsgamer.extrastorage.data.user.Storage;
import net.dv8tion.jda.api.entities.Message;
import net.dv8tion.jda.api.entities.channel.middleman.MessageChannel;
import net.dv8tion.jda.api.events.interaction.command.SlashCommandInteractionEvent;
import net.dv8tion.jda.api.events.message.MessageReceivedEvent;
import net.dv8tion.jda.api.interactions.commands.OptionMapping;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.application.PluginBoot;
import shyrcs.extrastoragehook.economy.EconomyProvider;
import shyrcs.extrastoragehook.executor.DiscordExecutor;
import shyrcs.extrastoragehook.SbMagicHook;

import java.text.DecimalFormat;
import java.util.Objects;
import java.util.UUID;

/**
 * Discord command để bán items từ kho ExtraStorage
 */
public class CommandSell extends DiscordExecutor {
    
    private static final DecimalFormat formatter = new DecimalFormat("#,###.##");
    
    public CommandSell() {
        super("sell", Library.config.getCommand("sell"));
    }
    
    @Override
    public void onSlashCommand(SlashCommandInteractionEvent event) {
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            event.reply(Library.config.getMessage("not-connected"))
                .setEphemeral(true).queue();
            return;
        }
        
        // Lấy parameters
        OptionMapping itemOption = event.getOption("item");
        OptionMapping amountOption = event.getOption("amount");
        
        if (itemOption == null) {
            event.reply("❌ Bạn cần chỉ định item cần bán!")
                .setEphemeral(true).queue();
            return;
        }
        
        String itemName = itemOption.getAsString();
        String amountStr = amountOption != null ? String.valueOf(amountOption.getAsInt()) : "1";
        
        new BukkitRunnable() {
            @Override
            public void run() {
                processSellCommand(event, null, authorId, itemName, amountStr);
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    @Override
    public void onChatCommand(MessageReceivedEvent event) {
        final MessageChannel channel = event.getChannel();
        final Message message = event.getMessage();
        final String authorId = Objects.requireNonNull(event.getMember()).getId();
        
        if (!Library.storage.userConnected(authorId)) {
            channel.sendMessage(Library.config.getMessage("not-connected"))
                .setMessageReference(message).queue();
            return;
        }
        
        // Parse arguments
        String[] args = message.getContentRaw().split("\\s+");
        if (args.length < 2) {
            channel.sendMessage("❌ Sử dụng: `!sell <item> [amount]`")
                .setMessageReference(message).queue();
            return;
        }
        
        String itemName = args[1];
        String amountStr = args.length > 2 ? args[2] : "1";
        
        new BukkitRunnable() {
            @Override
            public void run() {
                processSellCommand(null, event, authorId, itemName, amountStr);
            }
        }.runTaskAsynchronously(PluginBoot.main);
    }
    
    /**
     * Xử lý lệnh sell
     */
    private void processSellCommand(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, 
                                  String authorId, String itemName, String amountStr) {
        try {
            UUID playerUuid = Library.storage.getMinecraftUUID(authorId);
            if (playerUuid == null) {
                sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("not-connected"));
                return;
            }
            
            OfflinePlayer player = Bukkit.getOfflinePlayer(playerUuid);
            Storage storage = Library.extraStorageHook.getStorage(playerUuid);
            
            if (storage == null) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Không thể truy cập kho của bạn!");
                return;
            }
            
            // Parse material
            Material material = parseMaterial(itemName);
            if (material == null) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Item không hợp lệ: " + itemName);
                return;
            }
            
            String materialKey = material.name();
            
            // Kiểm tra item có trong kho không
            long availableAmount = storage.getQuantity(materialKey);
            if (availableAmount <= 0) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Bạn không có " + formatMaterialName(materialKey) + " trong kho!");
                return;
            }
            
            // Parse amount
            long sellAmount;
            if (amountStr.equalsIgnoreCase("all") || amountStr.equalsIgnoreCase("max")) {
                sellAmount = availableAmount;
            } else {
                try {
                    sellAmount = Long.parseLong(amountStr);
                    if (sellAmount <= 0) {
                        sendErrorMessage(slashEvent, chatEvent, "❌ Số lượng phải lớn hơn 0!");
                        return;
                    }
                } catch (NumberFormatException e) {
                    sendErrorMessage(slashEvent, chatEvent, "❌ Số lượng không hợp lệ: " + amountStr);
                    return;
                }
            }
            
            // Kiểm tra số lượng có đủ không
            if (sellAmount > availableAmount) {
                sendErrorMessage(slashEvent, chatEvent, 
                    "❌ Bạn chỉ có " + formatter.format(availableAmount) + " " + formatMaterialName(materialKey) + " trong kho!");
                return;
            }
            
            // Kiểm tra giới hạn sell
            int maxSellAmount = Library.config.getMaxSellAmount();
            if (sellAmount > maxSellAmount) {
                sendErrorMessage(slashEvent, chatEvent, 
                    "❌ Bạn chỉ có thể bán tối đa " + formatter.format(maxSellAmount) + " items mỗi lần!");
                return;
            }
            
            // Tạo ItemStack để tính giá
            ItemStack item = new ItemStack(material, (int) Math.min(sellAmount, Integer.MAX_VALUE));
            
            // Lấy economy provider và tính giá
            if (!Library.economyManager.hasProvider()) {
                sendErrorMessage(slashEvent, chatEvent, "❌ Hệ thống economy không khả dụng!");
                return;
            }

            EconomyProvider economyProvider = Library.economyManager.getCurrentProvider();
            
            // Sell item
            economyProvider.sellItem(player.getPlayer(), item, (int) sellAmount, result -> {
                if (!result.isSuccess()) {
                    sendErrorMessage(slashEvent, chatEvent, "❌ Không thể bán item này!");
                    return;
                }
                
                // Trừ item từ kho
                storage.subtract(materialKey, (int) sellAmount);
                
                // Gửi thông báo thành công
                String successMessage = Library.config.getMessage("sell-output")
                    .replace("{amount}", formatter.format(sellAmount))
                    .replace("{type}", Library.config.getEmote(materialKey) + " " + formatMaterialName(materialKey))
                    .replace("{price}", formatter.format(result.getPrice()));
                
                sendSuccessMessage(slashEvent, chatEvent, successMessage);
                
                SbMagicHook.info("Người chơi " + player.getName() + " đã bán " + sellAmount + " " + materialKey + 
                    " với giá " + result.getPrice());
            });
            
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi xử lý lệnh sell: " + e.getMessage());
            e.printStackTrace();
            sendErrorMessage(slashEvent, chatEvent, Library.config.getMessage("error"));
        }
    }
    
    /**
     * Parse material từ string
     */
    private Material parseMaterial(String input) {
        try {
            // Thử parse trực tiếp
            return Material.valueOf(input.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Thử với underscore
            try {
                return Material.valueOf(input.toUpperCase().replace(" ", "_"));
            } catch (IllegalArgumentException e2) {
                // Thử tìm material tương tự
                for (Material material : Material.values()) {
                    if (material.name().toLowerCase().contains(input.toLowerCase()) ||
                        material.name().toLowerCase().replace("_", "").equals(input.toLowerCase().replace(" ", ""))) {
                        return material;
                    }
                }
                return null;
            }
        }
    }
    

    
    /**
     * Format tên material
     */
    private String formatMaterialName(String materialKey) {
        String[] words = materialKey.toLowerCase().replace("_", " ").split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        return result.toString().trim();
    }
    
    /**
     * Gửi error message
     */
    private void sendErrorMessage(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, String message) {
        if (slashEvent != null) {
            if (!slashEvent.isAcknowledged()) {
                slashEvent.reply(message).setEphemeral(true).queue();
            }
        } else if (chatEvent != null) {
            chatEvent.getChannel().sendMessage(message)
                .setMessageReference(chatEvent.getMessage()).queue();
        }
    }
    
    /**
     * Gửi success message
     */
    private void sendSuccessMessage(SlashCommandInteractionEvent slashEvent, MessageReceivedEvent chatEvent, String message) {
        if (slashEvent != null) {
            if (!slashEvent.isAcknowledged()) {
                slashEvent.reply(message).setEphemeral(true).queue();
            }
        } else if (chatEvent != null) {
            chatEvent.getChannel().sendMessage(message)
                .setMessageReference(chatEvent.getMessage()).queue();
        }
    }
    

}
